name: Rebase arch branches to mater

on: 
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    steps:

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
      
      - name: Rebase and push
        run: |
            for branch in $(git for-each-ref --format='%(refname:short)' refs/remotes/origin/arch*); do
                git checkout ${branch#origin/}
                git rebase master
                git push --force
            done