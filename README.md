# 分子性质预测工具 (Molecule Property Predictor)

这是一个简单易用的分子性质预测工具，可以预测分子的毒性等属性。该工具借鉴了SME (Structure-Mask Explanation) 项目的思想，但进行了简化，使其更容易使用。

## 功能特点

- 从SMILES字符串预测分子属性
- 支持多种分子属性预测：
  - 致突变性 (Mutagenicity)
  - 心脏毒性 (hERG)
  - 水溶性 (ESOL)
  - 可自定义其他属性
- 提供简单的分子性质解释
- 支持批量预测
- 可训练自定义模型

## 安装

### 前提条件

- Python 3.7+
- PyTorch
- RDKit
- pandas
- numpy
- scikit-learn

### 安装步骤

1. 克隆或下载本仓库

```bash
git clone https://github.com/yourusername/molecule-property-predictor.git
cd molecule-property-predictor
```

2. 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 命令行工具

该工具提供了一个命令行界面，可以方便地进行预测、训练和查看可用模型。

#### 预测单个分子

```bash
python predict_molecule.py predict --smiles "CCO" --property Mutagenicity
```

#### 解释预测结果

```bash
python predict_molecule.py predict --smiles "CCO" --property Mutagenicity --explain
```

#### 批量预测

```bash
python predict_molecule.py predict --file molecules.csv --property hERG --output results.csv
```

#### 训练新模型

```bash
python predict_molecule.py train --data training_data.csv --property MyProperty --classification
```

#### 列出可用模型

```bash
python predict_molecule.py list
```

### Python API

您也可以在Python代码中直接使用该工具：

```python
from molecule_property_predictor import MoleculePredictor

# 创建预测器
predictor = MoleculePredictor()

# 预测分子属性
result = predictor.predict("CCO", "Mutagenicity")
print(result)

# 解释预测结果
explanation = predictor.explain_prediction("CCO", "Mutagenicity")
print(explanation)

# 训练新模型
predictor.train("my_data.csv", "MyProperty", is_classification=True)
```

## 数据格式

### 输入数据格式

训练数据应为CSV格式，至少包含以下列：
- `smiles`: SMILES字符串
- `属性名`: 要预测的属性值（例如 `Mutagenicity`, `hERG`, `ESOL`）
- `group` (可选): 数据分组（如 "training", "test", "valid"）

示例：

```csv
smiles,Mutagenicity,group
CCO,0,training
c1ccccc1,1,training
CC(=O)OC1=CC=CC=C1C(=O)O,0,test
```

### 输出数据格式

预测结果为JSON格式，包含以下字段：
- 分类任务：
  - `smiles`: 输入的SMILES字符串
  - `property`: 预测的属性名称
  - `prediction`: 预测结果（0或1）
  - `probability`: 预测概率
- 回归任务：
  - `smiles`: 输入的SMILES字符串
  - `property`: 预测的属性名称
  - `prediction`: 预测值

## 自定义模型

您可以通过修改 `molecule_property_predictor.py` 文件来自定义模型架构、特征提取方法等。

## 示例

### 预测阿司匹林的致突变性

```bash
python predict_molecule.py predict --smiles "CC(=O)OC1=CC=CC=C1C(=O)O" --property Mutagenicity --explain
```

输出：
```
SMILES: CC(=O)OC1=CC=CC=C1C(=O)O
属性: Mutagenicity
预测结果: 无毒性
概率: 0.1234

分子性质:
- molecular_weight: 180.159
- logP: 1.31
- num_h_donors: 1
- num_h_acceptors: 4
- rotatable_bonds: 3
- aromatic_rings: 1
- heavy_atoms: 13
```

## 许可证

MIT

## 致谢

本项目借鉴了SME (Structure-Mask Explanation)项目的思想，感谢原作者的工作。

