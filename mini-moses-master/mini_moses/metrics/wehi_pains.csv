"c:1:c:c(:c:c:c:1-[#6;X4]-c:2:c:c:c(:c:c:2)-[#7&H2,$([#7;!H0]-[#6;X4]),$([#7](-[#6X4])-[#6X4])])-[#7&H2,$([#7;!H0]-[#6;X4]),$([#7](-[#6X4])-[#6X4])]"                                                                                                                                                                                                                                                               ,"<regId=anil_di_alk_F(14)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#1])-[#7](-[#1])-[#1])-[#1])-[#1])-[#6]=[#7]-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                                ,"<regId=hzone_anil(14)>"
"c1(nn(c([c;!H0,$(c-[#6;!H0])]1)-[#8]-[#1])-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#1])-[#1])-[#1])-[#6;X4]"                                                                                                                                                                                                                                                                                                              ,"<regId=het_5_pyrazole_OH(14)>"
"c:2(:c:1-[#16]-c:3:c(-[#7;!H0,$([#7]-[CH3]),$([#7]-[#6;!H0;!H1]-[#6;!H0])](-c:1:c(:c(:c:2-[#1])-[#1])-[#1])):[c;!H0,$(c~[#7](-[#1])-[#6;X4]),$(c~[#6]:[#6])](:[c;!H0,$(c~[#6]:[#6])]:[c;!H0,$(c-[#7](-[#1])-[#1]),$(c-[#8]-[#6;X4])]:c:3-[#1]))-[#1]"                                                                                                                                                              ,"<regId=het_thio_666_A(13)>"
"[#6]-2-[#6]-c:1:c(:c:c:c:c:1)-[#6](-c:3:c:c:c:c:c-2:3)=[#6]-[#6]"                                                                                                                                                                                                                                                                                                                                                  ,"<regId=styrene_A(13)>"
"[#16]-1-[#6](=[#7]-[#6]:[#6])-[#7;!H0,$([#7]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#8]),$([#7]-[#6]:[#6])]-[#6](=[#8])-[#6]-1=[#6](-[#1])-[$([#6]:[#6]:[#6]-[#17]),$([#6]:[!#6&!#1])]"                                                                                                                                                                                                                            ,"<regId=ene_rhod_C(13)>"
"[#7](-[#1])(-[#1])-[#6]-1=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-[#6](=[#6](-[#6]=[#6])-[#8]-1)-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                ,"<regId=dhp_amino_CN_A(13)>"
"[#8]=[#16](=[#8])-[#6](-[#6]#[#7])=[#7]-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                                                                 ,"<regId=cyano_imine_C(12)>"
"c:1:c:c:c:c:c:1-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:2:c:c:c:c:c:2"                                                                                                                                                                                                                                                             ,"<regId=thio_urea_A(12)>"
"c:1:c(:c:c:c:c:1)-[#7](-[#1])-c:2:c(:c(:c(:s:2)-[$([#6]=[#8]),$([#6]#[#7]),$([#6](-[#8]-[#1])=[#6])])-[#7])-[$([#6]#[#7]),$([#6](:[#7]):[#7])]"                                                                                                                                                                                                                                                                    ,"<regId=thiophene_amino_B(12)>"
"[#6;X4]-1-[#6](=[#8])-[#7]-[#7]-[#6]-1=[#8]"                                                                                                                                                                                                                                                                                                                                                                       ,"<regId=keto_keto_beta_B(12)>"
"c:1:c-3:c(:c:c:c:1)-[#6]:2:[#7]:[!#1]:[#6]:[#6]:[#6]:2-[#6]-3=[#8]"                                                                                                                                                                                                                                                                                                                                                ,"<regId=keto_phenone_A(11)>"
"[#6]-1(-[#6](=[#6](-[#6]#[#7])-[#6](~[#8])~[#7]~[#6]-1~[#8])-[#6](-[#1])-[#1])=[#6](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                              ,"<regId=cyano_pyridone_C(11)>"
"[#6]-1(=[#6](-!@[#6]=[#7])-[#16]-[#6](-[#7]-1)=[#8])-[$([F,Cl,Br,I]),$([#7+](:[#6]):[#6])]"                                                                                                                                                                                                                                                                                                                        ,"<regId=thiaz_ene_C(11)>"
"c:1:2:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1]):[!#6&!#1]:[#6;!H0,$([#6]-[OH]),$([#6]-[#6;H2,H3])](:[#6]:2-[#6](-[#1])=[#7]-[#7](-[#1])-[$([#6]:1:[#7]:[#6]:[#6](-[#1]):[#16]:1),$([#6]:[#6](-[#1]):[#6]-[#1]),$([#6]:[#7]:[#6]:[#7]:[#6]:[#7]),$([#6]:[#7]:[#7]:[#7]:[#7])])"                                                                                                                                       ,"<regId=hzone_thiophene_A(11)>"
"[!#1]:[!#1]-[#6;!H0,$([#6]-[#6]#[#7])]=[#6]-1-[#6]=,:[#6]-[#6](=[$([#8]),$([#7;!R])])-[#6]=,:[#6]-1"                                                                                                                                                                                                                                                                                                               ,"<regId=ene_quin_methide(10)>"
"c:1:c:c-2:c(:c:c:1)-[#6]-[#6](-c:3:c(-[#16]-2):c(:c(-[#1]):[c;!H0,$(c-[#8]),$(c-[#16;X2]),$(c-[#6;X4]),$(c-[#7;H2,H3,$([#7!H0]-[#6;X4]),$([#7](-[#6;X4])-[#6;X4])])](:c:3-[#1]))-[#1])-[#7;H2,H3,$([#7;!H0]-[#6;X4]),$([#7](-[#6;X4])-[#6;X4])]"                                                                                                                                                                   ,"<regId=het_thio_676_A(10)>"
"[#6]-1(=[#8])-[#6](=[#6](-[#1])-[$([#6]:1:[#6]:[#6]:[#6]:[#6]:[#6]:1),$([#6]:1:[#6]:[#6]:[#6]:[!#6&!#1]:1)])-[#7]=[#6](-[!#1]:[!#1]:[!#1])-[$([#16]),$([#7]-[!#1]:[!#1])]-1"                                                                                                                                                                                                                                       ,"<regId=ene_five_het_G(10)>"
"[#7+](:[!#1]:[!#1]:[!#1])-[!#1]=[#8]"                                                                                                                                                                                                                                                                                                                                                                              ,"<regId=acyl_het_A(9)>"
"[#6;X4]-[#7](-[#6;X4])-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6]2=,:[#7][#6]:[#6]:[!#1]2)-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                   ,"<regId=anil_di_alk_G(9)>"
"[#7;!H0,$([#7]-[#6;X4])]-1-[#6]=,:[#6](-[#6](=[#8])-[#6]:[#6]:[#6])-[#6](-[#6])-[#6](=[#6]-1-[#6](-[#1])(-[#1])-[#1])-[$([#6]=[#8]),$([#6]#[#7])]"                                                                                                                                                                                                                                                                 ,"<regId=dhp_keto_A(9)>"
"c:1:c:c:c:c:c:1-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:2:c:c:c:c:c:2"                                                                                                                                                                                                                                                                                ,"<regId=thio_urea_B(9)>"
"c:1:3:c(:c(:c(:c(:c:1-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#1])-c:2:c:c:c:c:c:2)-[#1]):n:c(-[#1]):n:3-[#6]"                                                                                                                                                                                                                                                                                                       ,"<regId=anil_alk_bim(9)>"
"c:1:c:c-2:c(:c:c:1)-[#7]=[#6]-[#6]-2=[#7;!R]"                                                                                                                                                                                                                                                                                                                                                                      ,"<regId=imine_imine_A(9)>"
"c:1(:c:c:c:c:c:1)-[#7](-[#1])-[#6](=[#16])-[#7]-[#7](-[#1])-[#6](=[#8])-[#6]-,:2:[!#1]:[!#6&!#1]:[#6]:[#6]-,:2"                                                                                                                                                                                                                                                                                                    ,"<regId=thio_urea_C(9)>"
"[#7;!R]=[#6]-2-[#6](=[#8])-c:1:c:c:c:c:c:1-[#16]-2"                                                                                                                                                                                                                                                                                                                                                                ,"<regId=imine_one_fives_B(9)>"
"[$([#7](-[#1])-[#1]),$([#8]-[#1])]-[#6]-2=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-c:1:c(:n(-[#6]):n:c:1)-[#8]-2"                                                                                                                                                                                                                                                                                                  ,"<regId=dhp_amino_CN_B(9)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:n:c:1-[#1])-[#8]-c:2:c:c:c:c:c:2)-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                      ,"<regId=anil_OC_no_alk_A(8)>"
"[#6](=[#8])-[#6]-1=[#6]-[#7]-c:2:c(-[#16]-1):c:c:c:c:2"                                                                                                                                                                                                                                                                                                                                                            ,"<regId=het_thio_66_one(8)>"
"c:1:c:c-2:c(:c:c:1)-[#6](-c:3:c(-[$([#16;X2]),$([#6;X4])]-2):c:c:[c;!H0,$(c-[#17]),$(c-[#6;X4])](:c:3))=[#6]-[#6]"                                                                                                                                                                                                                                                                                                 ,"<regId=styrene_B(8)>"
"[#6](-[#1])(-[#1])-[#16;X2]-c:1:n:c(:c(:n:1-!@[#6](-[#1])-[#1])-c:2:c:c:c:c:c:2)-[#1]"                                                                                                                                                                                                                                                                                                                             ,"<regId=het_thio_5_A(8)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6]-2=[#6](-[#1])-c:1:c(:c:c:c:c:1)-[#16;X2]-c:3:c-2:c:c:c:c:3"                                                                                                                                                                                                                                                                                                        ,"<regId=anil_di_alk_ene_A(8)>"
"[#16]-1-[#6](=!@[#7;!H0,$([#7]-[#7](-[#1])-[#6]:[#6])])-[#7;!H0,$([#7]-[#6]:[#7]:[#6]:[#6]:[#16])]-[#6](=[#8])-[#6]-1=[#6](-[#1])-[#6]:[#6]-[$([#17]),$([#8]-[#6]-[#1])]"                                                                                                                                                                                                                                          ,"<regId=ene_rhod_D(8)>"
"[#16]-1-[#6](=[#8])-[#7]-[#6](=[#16])-[#6]-1=[#6](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                                ,"<regId=ene_rhod_E(8)>"
"c:1:c(:c:c:c:c:1)-[#6](-[#1])(-[#1])-[#7](-[#1])-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#8]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                            ,"<regId=anil_OH_alk_A(8)>"
"n1(-[#6;X4])c(c(-[#1])c(c1-[#6]:[#6])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=pyrrole_C(8)>"
"c:1(:c:c:c:c:c:1)-[#7](-[#1])-[#6](=[#16])-[#7]-[#7](-[#1])-c:2:c:c:c:c:c:2"                                                                                                                                                                                                                                                                                                                                       ,"<regId=thio_urea_D(8)>"
"[#7](-c:1:c:c:c:c:c:1)-c2[n+]c(cs2)-c:3:c:c:c:c:c:3"                                                                                                                                                                                                                                                                                                                                                               ,"<regId=thiaz_ene_D(8)>"
"n:1:c:c:c(:c:1-[#6](-[#1])-[#1])-[#6](-[#1])=[#6]-2-[#6](=[#8])-[#7]-[#6](=[!#6&!#1])-[#7]-2"                                                                                                                                                                                                                                                                                                                      ,"<regId=ene_rhod_F(8)>"
"[#6]-,:1(=,:[#6](-[#6](-[#1])(-[#6])-[#6])-,:[#16]-,:[#6](-,:[#7;!H0,$([#7]-[#6;!H0;!H1])]-,:1)=[#8])-[#16]-[#6;R]"                                                                                                                                                                                                                                                                                                ,"<regId=thiaz_ene_E(8)>"
"[!#1]:,-1:[!#1]-,:2:[!#1](:[!#1]:[!#1]:[!#1]:,-1)-,:[#7](-[#1])-,:[#7](-,:[#6]-,:2=[#8])-[#6]"                                                                                                                                                                                                                                                                                                                     ,"<regId=het_65_B(7)>"
"c:1:c:c-2:c(:c:c:1)-[#6](=[#6](-[#6]-2=[#8])-[#6])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=keto_keto_beta_C(7)>"
"c:2:c:c:1:n:n:c(:n:c:1:c:c:2)-[#6](-[#1])(-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=het_66_A(7)>"
"c:1:c:c:c:c:c:1-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-[#6](-[#1])(-[#1])-c:2:n:c:c:c:c:2"                                                                                                                                                                                                                                                                                                                           ,"<regId=thio_urea_E(7)>"
"[#6](-[#1])-[#6](-[#1])(-[#1])-c:1:c(:c(:c(:s:1)-[#7](-[#1])-[#6](=[#8])-[#6]-[#6]-[#6]=[#8])-[$([#6](=[#8])-[#8]),$([#6]#[#7])])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                ,"<regId=thiophene_amino_C(7)>"
"[#6](-c:1:c(:c(:[c;!H0,$(c-[#6;X4])]:c:c:1-[#1])-[#1])-[#1])(-c:2:c(:c(:[c;!H0,$(c-[#17])](:c(:c:2-[#1])-[#1]))-[#1])-[#1])=[$([#7]-[#8]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]),$([#7]-[#8]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]),$([#7]-[#7](-[#1])-[#6](=[#7]-[#1])-[#7](-[#1])-[#1]),$([#6](-[#1])-[#7])]","<regId=hzone_phenone(7)>"
"[#8](-[#1])-[#6](=[#8])-c:1:c:c(:c:c:c:1)-[#6]:[!#1]:[#6]-[#6](-[#1])=[#6]-2-[#6](=[!#6&!#1])-[#7]-[#6](=[!#6&!#1])-[!#6&!#1]-2"                                                                                                                                                                                                                                                                                   ,"<regId=ene_rhod_G(7)>"
"[#6]-1(=[#6]-[#6](-c:2:c:c(:c(:n:c-1:2)-[#7](-[#1])-[#1])-[#6]#[#7])=[#6])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                              ,"<regId=ene_cyano_B(7)>"
"[#7](-[#1])(-[#1])-[#6]-1=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-[#6](=[#6](-[#6]:[#6])-[#8]-1)-[#6]#[#7]"                                                                                                                                                                                                                                                                                                       ,"<regId=dhp_amino_CN_C(7)>"
"[#7]-2(-c:1:c:c:c:c:c:1)-[#7]=[#6](-[#6]=[#8])-[#6;X4]-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                                                                ,"<regId=het_5_A(7)>"
"[#7]-1=[#6]-[#6](-[#6](-[#7]-1)=[#16])=[#6]"                                                                                                                                                                                                                                                                                                                                                                       ,"<regId=ene_five_het_H(6)>"
"c1(coc(c1-[#1])-[#6](=[#16])-[#7]-2-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[!#1]-[#6](-[#1])(-[#1])-[#6]-2(-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                       ,"<regId=thio_amide_A(6)>"
"[#6]=[#6](-[#6]#[#7])-[#6](=[#7]-[#1])-[#7]-[#7]"                                                                                                                                                                                                                                                                                                                                                                  ,"<regId=ene_cyano_C(6)>"
"c:1(:c(:c(:[c;!H0,$(c-[#6;!H0;!H1])](:o:1))-[#1])-[#1])-[#6;!H0,$([#6]-[#6;!H0;!H1])]=[#7]-[#7](-[#1])-c:2:n:c:c:s:2"                                                                                                                                                                                                                                                                                              ,"<regId=hzone_furan_A(6)>"
"c:1(:c(:c(:c(:c(:c:1-[#7](-[#1])-[#16](=[#8])(=[#8])-[#6]:2:[#6]:[!#1]:[#6]:[#6]:[#6]:2)-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                         ,"<regId=anil_di_alk_H(6)>"
"n2c1ccccn1c(c2-[$([#6](-[!#1])=[#6](-[#1])-[#6]:[#6]),$([#6]:[#8]:[#6])])-[#7]-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                          ,"<regId=het_65_C(6)>"
"[#6]-1-[#7](-[#1])-[#7](-[#1])-[#6](=[#16])-[#7]-[#7]-1-[#1]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=thio_urea_F(6)>"
"c:1(:c:c:c:o:1)-[#6](-[#1])=!@[#6]-3-[#6](=[#8])-c:2:c:c:c:c:c:2-[!#6&!#1]-3"                                                                                                                                                                                                                                                                                                                                      ,"<regId=ene_five_het_I(6)>"
"[#8]=[#6]-1-[#6;X4]-[#6]-[#6](=[#8])-c:2:c:c:c:c:c-1:2"                                                                                                                                                                                                                                                                                                                                                            ,"<regId=keto_keto_gamma(5)>"
"c:1:c:c-2:c(:c:c:1)-[#6](-c3cccc4noc-2c34)=[#8]"                                                                                                                                                                                                                                                                                                                                                                   ,"<regId=quinone_B(5)>"
"[#8](-[#1])-c:1:n:c(:c:c:c:1)-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                           ,"<regId=het_6_pyridone_OH(5)>"
"c:1:2:c(:c(:c(:c(:c:1:c(:c(:c(:c:2-[#1])-[#1])-[#6]=[#7]-[#7](-[#1])-[$([#6]:[#6]),$([#6]=[#16])])-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                   ,"<regId=hzone_naphth_A(5)>"
"[#6]-,:1=,:[#6](-,:[#16]-,:[#6](-,:[#6]=,:[#6]-,:1)=[#16])-,:[#7]"                                                                                                                                                                                                                                                                                                                                                 ,"<regId=thio_ester_A(5)>"
"[#6]-1=[#6]-[#6](-[#8]-[#6]-1-[#8])(-[#8])-[#6]"                                                                                                                                                                                                                                                                                                                                                                   ,"<regId=ene_misc_A(5)>"
"[#8]=[#6]-,:1-,:[#6](=,:[#6]-,:[#6](=,:[#7]-,:[#7]-,:1)-,:[#6]=[#8])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=cyano_pyridone_D(5)>"
"c3cn1c(nc(c1-[#7]-[#6])-c:2:c:c:c:c:n:2)cc3"                                                                                                                                                                                                                                                                                                                                                                       ,"<regId=het_65_Db(5)>"
"[#7]-2-c:1:c:c:c:c:c:1-[#6](=[#7])-c:3:c-2:c:c:c:c:3"                                                                                                                                                                                                                                                                                                                                                              ,"<regId=het_666_A(5)>"
"c:1:c(:c:c:c:c:1)-[#7]-2-[#6](-[#1])-[#6](-[#1])-[#7](-[#6](-[#1])-[#6]-2-[#1])-[#16](=[#8])(=[#8])-c:3:c:c:c:c:4:n:s:n:c:3:4"                                                                                                                                                                                                                                                                                     ,"<regId=diazox_sulfon_B(5)>"
"c:1(:c(:c-,:2:c(:c(:c:1-[#1])-[#1])-,:[#7](-,:[#6](-,:[#7]-,:2-[#1])=[#8])-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                               ,"<regId=anil_NH_alk_A(5)>"
"c:1(:c(:c-3:c(:c(:c:1-[#7](-[#1])-[#16](=[#8])(=[#8])-c:2:c:c:c(:c:c:2)-[!#6&!#1])-[#1])-[#8]-[#6](-[#8]-3)(-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                               ,"<regId=sulfonamide_C(5)>"
"[#6](-[#1])-[#6]:2:[#7]:[#7](-c:1:c:c:c:c:c:1):[#16]:3:[!#6&!#1]:[!#1]:[#6]:[#6]:2:3"                                                                                                                                                                                                                                                                                                                              ,"<regId=het_thio_N_55(5)>"
"[#8]=[#6]-[#6]=[#6](-[#1])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                              ,"<regId=keto_keto_beta_D(5)>"
"[#7]-,:1-,:2-,:[#6](=,:[#7]-,:[#6](=[#8])-,:[#6](=,:[#7]-,:1)-[#6](-[#1])-[#1])-,:[#16]-,:[#6](=[#6](-[#1])-[#6]:[#6])-,:[#6]-,:2=[#8]"                                                                                                                                                                                                                                                                            ,"<regId=ene_rhod_H(5)>"
"[#6]:[#6]-[#6](-[#1])=[#6](-[#1])-[#6](-[#1])=[#7]-[#7](-[#6;X4])-[#6;X4]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=imine_ene_A(5)>"
"c:1:3:c(:c:c:c:c:1):c:2:n:n:c(-[#16]-[#6](-[#1])(-[#1])-[#6]=[#8]):n:c:2:n:3-[#6](-[#1])(-[#1])-[#6](-[#1])=[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                      ,"<regId=het_thio_656a(5)>"
"n1(-[#6])c(c(-[#1])c(c1-[#6](-[#1])(-[#1])-[#7](-[#1])-[#6](=[#16])-[#7]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                         ,"<regId=pyrrole_D(5)>"
"n2(-[#6]:1:[!#1]:[!#6&!#1]:[!#1]:[#6]:1-[#1])c(c(-[#1])c(c2-[#6;X4])-[#1])-[#6;X4]"                                                                                                                                                                                                                                                                                                                                ,"<regId=pyrrole_E(5)>"
"c:1(:c:c:c:c:c:1)-[#7](-[#1])-[#6](=[#16])-[#7]-[#7](-[#1])-[#6]([#7;R])[#7;R]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=thio_urea_G(5)>"
"c:1(:c(:c(:c(:c(:[c;!H0,$(c-[#6](-[#1])-[#1])]:1)-[#1])-[#8]-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[$([#7](-[#1])-[#6](=[#8])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1]),$([#6](-[#1])(-[#6](-[#1])-[#1])-[#7](-[#1])-[#6](=[#16])-[#7]-[#1])])-[#1])-[#8]-[#6](-[#1])-[#1]"                                                                                                                           ,"<regId=anisol_A(5)>"
"n2(-[#6]:1:[#6](-[#6]#[#7]):[#6]:[#6]:[!#6&!#1]:1)c(c(-[#1])c(c2)-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                      ,"<regId=pyrrole_F(5)>"
"[#7](-[#1])(-[#1])-[#6]-2=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-c:1:c(:c:c:s:1)-[#8]-2"                                                                                                                                                                                                                                                                                                                         ,"<regId=dhp_amino_CN_D(5)>"
"[#7](-[#1])-c:1:n:c(:c:s:1)-c:2:c:n:c(-[#7](-[#1])-[#1]):s:2"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=thiazole_amine_A(4)>"
"[#7]=[#6]-1-[#7](-[#1])-[#6](=[#6](-[#7]-[#1])-[#7]=[#7]-1)-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                                             ,"<regId=het_6_imidate_A(4)>"
"c:1:c(:c:2:c(:c:c:1):c:c:c:c:2)-[#8]-c:3:c(:c(:c(:c(:c:3-[#1])-[#1])-[#7]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                        ,"<regId=anil_OC_no_alk_B(4)>"
"c:1:c:c-2:c(:c:c:1)-[#6]-[#16]-c3c(-[#6]-2=[#6])ccs3"                                                                                                                                                                                                                                                                                                                                                              ,"<regId=styrene_C(4)>"
"c:2:c:c:c:1:c(:c:c:c:1):c:c:2"                                                                                                                                                                                                                                                                                                                                                                                     ,"<regId=azulene(4)>"
"c:1(:c(:c(:c(:o:1)-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#8]-[#6]:[#6])-[#1])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                               ,"<regId=furan_acid_A(4)>"
"[!#1]:[#6]-[#6]-,:1=,:[#6](-[#1])-,:[#6](=,:[#6](-[#6]#[#7])-,:[#6](=[#8])-,:[#7]-,:1-[#1])-[#6]:[#8]"                                                                                                                                                                                                                                                                                                             ,"<regId=cyano_pyridone_E(4)>"
"[#6]-1-3=[#6](-[#6](-[#7]-c:2:c:c:c:c:c-1:2)(-[#6])-[#6])-[#16]-[#16]-[#6]-3=[!#1]"                                                                                                                                                                                                                                                                                                                                ,"<regId=anil_alk_thio(4)>"
"c:1(:c(:c(:c(:c(:c:1-[#7](-[#1])-[#6](=[#8])-c:2:c:c:c:c:c:2)-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                    ,"<regId=anil_di_alk_I(4)>"
"[#6](-[#1])(-[#1])-[#16;X2]-c:1:n:n:c(:c(:n:1)-c:2:c(:c(:c(:o:2)-[#1])-[#1])-[#1])-c:3:c(:c(:c(:o:3)-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                             ,"<regId=het_thio_6_furan(4)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6]-2=[#6]-c:1:c(:c:c:c:c:1)-[#6]-2(-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                       ,"<regId=anil_di_alk_ene_B(4)>"
"[#7](-[#1])(-c:1:c:c:c:c:c:1)-[#7]=[#6](-[#6](=[#8])-[#6](-[#1])-[#1])-[#7](-[#1])-[$([#7]-[#1]),$([#6]:[#6])]"                                                                                                                                                                                                                                                                                                    ,"<regId=imine_one_B(4)>"
"c:1:2:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1]):o:c:3:c(-[#1]):c(:c(-[#8]-[#6](-[#1])-[#1]):c(:c:2:3)-[#1])-[#7](-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                            ,"<regId=anil_OC_alk_A(4)>"
"[#16]=[#6]-,:1-,:[#7](-[#1])-,:[#6]=,:[#6]-,:[#6]-2=,:[#6]-,:1-[#6](=[#8])-[#8]-[#6]-2=[#6]-[#1]"                                                                                                                                                                                                                                                                                                                  ,"<regId=ene_five_het_J(4)>"
"n2(-c:1:c(:c:c(:c(:c:1)-[#1])-[$([#7](-[#1])-[#1]),$([#6]:[#7])])-[#1])c(c(-[#1])c(c2-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                            ,"<regId=pyrrole_G(4)>"
"n1(-[#6])c(c(-[#1])c(c1-[#6](-[#1])=[#6]-2-[#6](=[#8])-[!#6&!#1]-[#6]=,:[!#1]-2)-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                       ,"<regId=ene_five_het_K(4)>"
"[#6]=[#6]-[#6](-[#6]#[#7])(-[#6]#[#7])-[#6](-[#6]#[#7])=[#6]-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=cyano_ene_amine_B(4)>"
"[#6]:[#6]-[#6](=[#16;X1])-[#16;X2]-[#6](-[#1])-[$([#6](-[#1])-[#1]),$([#6]:[#6])]"                                                                                                                                                                                                                                                                                                                                 ,"<regId=thio_ester_B(4)>"
"[#8]=[#6]-3-[#6](=!@[#6](-[#1])-c:1:c:n:c:c:1)-c:2:c:c:c:c:c:2-[#7]-3"                                                                                                                                                                                                                                                                                                                                             ,"<regId=ene_five_het_L(4)>"
"c:1(:[c;!H0,$(c-[#6;!H0;!H1])](:c(:c(:s:1)-[#1])-[#1]))-[#6](-[#1])=[#7]-[#7](-[#1])-c:2:c:c:c:c:c:2"                                                                                                                                                                                                                                                                                                              ,"<regId=hzone_thiophene_B(4)>"
"[#6](-[#1])(-[#1])-[#16;X2]-[#6]-1=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-[#6](-[#6]#[#7])-[#6](=[#8])-[#7]-1"                                                                                                                                                                                                                                                                                                   ,"<regId=dhp_amino_CN_E(4)>"
"[#7]-2(-c:1:c:c:c:c:c:1)-[#7]=[#6](-[#7](-[#1])-[#6]=[#8])-[#6](-[#1])(-[#1])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                                         ,"<regId=het_5_B(4)>"
"[#6]:[#6]-[#6](-[#1])=[#6](-[#1])-[#6](-[#1])=[#7]-[#7]=[#6]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=imine_imine_B(3)>"
"c:1(:c:c:c(:c:c:1)-[#6](-[#1])-[#1])-c:2:c(:s:c(:n:2)-[#7](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                   ,"<regId=thiazole_amine_B(3)>"
"[#6]-2(-[#6]=[#7]-c:1:c:c:c:c:c:1-[#7]-2)=[#6](-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                                                   ,"<regId=imine_ene_one_A(3)>"
"[#8](-c:1:c:c:c:c:c:1)-c:3:c:c:2:n:o:n:c:2:c:c:3"                                                                                                                                                                                                                                                                                                                                                                  ,"<regId=diazox_A(3)>"
"[!#1]:1:[!#1]:[!#1]:[!#1](:[!#1]:[!#1]:1)-[#6](-[#1])=[#6](-[#1])-[#6](-[#7]-c:2:c:c:c:3:c(:c:2):c:c:c(:n:3)-[#7](-[#6])-[#6])=[#8]"                                                                                                                                                                                                                                                                               ,"<regId=ene_one_A(3)>"
"[#7](-[#1])(-[#1])-c:1:c(:c:c:c:n:1)-[#8]-[#6](-[#1])(-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                            ,"<regId=anil_OC_no_alk_C(3)>"
"[#6]-[#16;X2]-c:1:n:c(:c:s:1)-[#1]"                                                                                                                                                                                                                                                                                                                                                                                ,"<regId=thiazol_SC_A(3)>"
"c:1:c-3:c(:c:c:c:1)-[#7](-c:2:c:c:c:c:c:2-[#8]-3)-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                             ,"<regId=het_666_B(3)>"
"c:1(:c(:c(:c(:o:1)-[#6](-[#1])-[#1])-[#1])-[#1])-[#6](-[#1])(-[#8]-[#1])-[#6]#[#6]-[#6;X4]"                                                                                                                                                                                                                                                                                                                        ,"<regId=furan_A(3)>"
"[#6]-1(-[#6](=[#6]-[#6]=[#6]-[#6]=[#6]-1)-[#7]-[#1])=[#7]-[#6]"                                                                                                                                                                                                                                                                                                                                                    ,"<regId=colchicine_A(3)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])=[#6]-[#6](=[#8])-c:1:c(-[#16;X2]):s:c(:c:1)-[$([#6]#[#7]),$([#6]=[#8])]"                                                                                                                                                                                                                                                                                    ,"<regId=thiophene_C(3)>"
"c:1:3:c(:c:c:c:c:1)-[#7]-2-[#6](=[#8])-[#6](=[#6](-[F,Cl,Br,I])-[#6]-2=[#8])-[#7](-[#1])-[#6]:[#6]:[#6]:[#6](-[#8]-[#6](-[#1])-[#1]):[#6]:[#6]:3"                                                                                                                                                                                                                                                                  ,"<regId=anil_OC_alk_B(3)>"
"c:1-2:c(:c:c:c:c:1)-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7]=[#6]-2-[#16;X2]-[#6](-[#1])(-[#1])-[#6](=[#8])-c:3:c:c:c:c:c:3"                                                                                                                                                                                                                                                                                     ,"<regId=het_thio_66_A(3)>"
"[#7]-2(-c:1:c:c:c:c:c:1-[#6](-[#1])-[#1])-[#6](=[#16])-[#7](-[#6](-[#1])(-[#1])-[!#1]:[!#1]:[!#1]:[!#1]:[!#1])-[#6](-[#1])(-[#1])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                     ,"<regId=rhod_sat_B(3)>"
"[#7]-2(-[#6](-[#1])-[#1])-[#6](=[#16])-[#7](-[#1])-[#6](=[#6](-[#1])-c:1:c:c:c:c(:c:1)-[Br])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                          ,"<regId=ene_rhod_I(3)>"
"c:1(:c(:c:2:c(:s:1):c:c:c:c:2)-[#6](-[#1])-[#1])-[#6](=[#8])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                  ,"<regId=keto_thiophene(3)>"
"[#7](-[#6](-[#1])-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])=[#7]-[#6](-[#6](-[#1])-[#1])=[#7]-[#7](-[#6](-[#1])-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                        ,"<regId=imine_imine_C(3)>"
"[#6]:2(:[#6](-[#6](-[#1])-[#1]):[#6]-,:1:[#6](-,:[#7]=,:[#6;!H0,$([#6]-[#16]-[#6](-[#1])-[#1])](-,:[#7](-,:[#6]-,:1=[!#6&!#1;X1])-[#6](-[#1])-[$([#6](=[#8])-[#8]),$([#6]:[#6])])):[!#6&!#1;X2]:2)-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                            ,"<regId=het_65_pyridone_A(3)>"
"c:1(:n:c(:c(-[#1]):s:1)-[!#1]:[!#1]:[!#1](-[$([#8]-[#6](-[#1])-[#1]),$([#6](-[#1])-[#1])]):[!#1]:[!#1])-[#7](-[#1])-[#6](-[#1])(-[#1])-c:2:c(-[#1]):c(:c(-[#1]):o:2)-[#1]"                                                                                                                                                                                                                                         ,"<regId=thiazole_amine_C(3)>"
"n:1:c(:c(:c(:c(:c:1-[#16]-[#6]-[#1])-[#6]#[#7])-c:2:c:c:c(:c:c:2)-[#8]-[#6](-[#1])-[#1])-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                          ,"<regId=het_thio_pyr_A(3)>"
"c:1:4:c(:n:c(:n:c:1-[#7](-[#1])-[#6](-[#1])(-[#1])-c:2:c(:c(:c(:o:2)-[#1])-[#1])-[#1])-[#7](-[#1])-c:3:c:[c;!H0,$(c-[#6](-[#1])-[#1]),$(c-[#16;X2]),$(c-[#8]-[#6]-[#1]),$(c-[#7;X3])](:[c;!H0,$(c-[#6](-[#1])-[#1]),$(c-[#16;X2]),$(c-[#8]-[#6]-[#1]),$(c-[#7;X3])](:c:[c;!H0,$(c-[#6](-[#1])-[#1]),$(c-[#16;X2]),$(c-[#8]-[#6]-[#1]),$(c-[#7;X3])]:3))):c:c:c:c:4"                                                ,"<regId=melamine_A(3)>"
"[#7](-[#1])(-[#6]:1:[#6]:[#6]:[!#1]:[#6]:[#6]:1)-c:2:c:c:c(:c:c:2)-[#7](-[#1])-[#6]-[#1]"                                                                                                                                                                                                                                                                                                                          ,"<regId=anil_NH_alk_B(3)>"
"[#7]-2(-c:1:c:c:c:c:c:1)-[#6](=[#7]-[#6]=[#8])-[#16]-[#6](-[#1])(-[#1])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                                               ,"<regId=rhod_sat_C(3)>"
"[#6]=[#6]-[#6](=[#8])-[#7]-c:1:c(:c(:c(:s:1)-[#6](=[#8])-[#8])-[#6]-[#1])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                               ,"<regId=thiophene_amino_D(3)>"
"[#8;!H0,$([#8]-[#6](-[#1])-[#1])]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#1])-c:2:n:c:c:n:2"                                                                                                                                                                                                                                                                                        ,"<regId=anil_OC_alk_C(3)>"
"[#6](-[#1])(-[#1])-[#16;X2]-c3nc1c(n(nc1-[#6](-[#1])-[#1])-c:2:c:c:c:c:c:2)nn3"                                                                                                                                                                                                                                                                                                                                    ,"<regId=het_thio_65_A(3)>"
"[#6]-[#6](=[#8])-[#6](-[#1])(-[#1])-[#16;X2]-c:3:n:n:c:2:c:1:c(:c(:c(:c(:c:1:n(:c:2:n:3)-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                             ,"<regId=het_thio_656b(3)>"
"s:1:c(:[n+](-[#6](-[#1])-[#1]):c(:c:1-[#1])-[#6])-[#7](-[#1])-c:2:c:c:c:c:c:2[$([#6](-[#1])-[#1]),$([#6]:[#6])]"                                                                                                                                                                                                                                                                                                   ,"<regId=thiazole_amine_D(3)>"
"[#6]-,:2(=[#16])-,:[#7](-[#6](-[#1])(-[#1])-c:1:c:c:c:o:1)-,:[#6](=,:[#7]-,:[#7]-,:2-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                              ,"<regId=thio_urea_H(3)>"
"[#7]-,:2(-c:1:c:c:c:c:c:1)-,:[#6](=[#8])-,:[#6](=,:[#6]-,:[#6](=,:[#7]-,:2)-[#6]#[#7])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                  ,"<regId=cyano_pyridone_F(3)>"
"[#7]-2(-c:1:c:c:c:c:c:1)-[#6](=[#8])-[#16]-[#6](-[#1])(-[#6](-[#1])(-[#1])-[#6](=[#8])-[#7](-[#1])-[#6]:[#6])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                         ,"<regId=rhod_sat_D(3)>"
"[#6](-[#1])(-[#1])-[#7]-2-[#6](=[$([#16]),$([#7])])-[!#6&!#1]-[#6](=[#6]-1-[#6](=[#6](-[#1])-[#6]:[#6]-[#7]-1-[#6](-[#1])-[#1])-[#1])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                 ,"<regId=ene_rhod_J(3)>"
"[#6]=[#7;!R]-c:1:c:c:c:c:c:1-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                            ,"<regId=imine_phenol_A(3)>"
"[#8]=[#6]-,:2-,:[#16]-,:c:1:c(:c(:c:c:c:1)-[#8]-[#6](-[#1])-[#1])-,:[#8]-,:2"                                                                                                                                                                                                                                                                                                                                      ,"<regId=thio_carbonate_B(3)>"
"[#7]=,:[#6]-,:1-,:[#7]=,:[#6]-,:[#7]-,:[#16]-,:1"                                                                                                                                                                                                                                                                                                                                                                  ,"<regId=het_thio_N_5A(3)>"
"[#7]-,:2-,:[#16]-,:[#6]-1=,:[#6](-[#6]:[#6]-[#7]-[#6]-1)-,:[#6]-,:2=[#16]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=het_thio_N_65A(3)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](-[#1])=[#7]-[#7]=[#6](-[#6])-[#6]:[#6])-[#1])-[#1]"                                                                                                                                                                                                                                                                                ,"<regId=anil_di_alk_J(3)>"
"n1-2cccc1-[#6]=[#7](-[#6])-[#6]-[#6]-2"                                                                                                                                                                                                                                                                                                                                                                            ,"<regId=pyrrole_H(3)>"
"[#6](-[#6]#[#7])(-[#6]#[#7])=[#6](-[#16])-[#16]"                                                                                                                                                                                                                                                                                                                                                                   ,"<regId=ene_cyano_D(3)>"
"[#6]-1(-[#6]#[#7])(-[#6]#[#7])-[#6](-[#1])(-[#6](=[#8])-[#6])-[#6]-1-[#1]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=cyano_cyano_B(3)>"
"[#6]-1=,:[#6]-[#6](-[#6](-[$([#8]),$([#16])]-1)=[#6]-[#6]=[#8])=[#8]"                                                                                                                                                                                                                                                                                                                                              ,"<regId=ene_five_het_M(3)>"
"[#6]:[#6]-[#6](=[#8])-[#7](-[#1])-[#6](=[#8])-[#6](-[#6]#[#7])=[#6](-[#1])-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                  ,"<regId=cyano_ene_amine_C(3)>"
"c:1(:c:c:c:c:c:1)-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-[#7]=[#6]-c:2:c:n:c:c:2"                                                                                                                                                                                                                                                                                                                                    ,"<regId=thio_urea_I(3)>"
"[#7](-[#1])(-[#1])-[#6]-2=[#6](-[#6]#[#7])-[#6](-[#1])(-c:1:c:c:c:s:1)-[#6](=[#6](-[#6](-[#1])-[#1])-[#8]-2)-[#6](=[#8])-[#8]-[#6]"                                                                                                                                                                                                                                                                                ,"<regId=dhp_amino_CN_F(3)>"
"c:1:c-3:c(:c:c(:c:1)-[#6](=[#8])-[#7](-[#1])-c:2:c(:c:c:c:c:2)-[#6](=[#8])-[#8]-[#1])-[#6](-[#7](-[#6]-3=[#8])-[#6](-[#1])-[#1])=[#8]"                                                                                                                                                                                                                                                                             ,"<regId=anthranil_acid_B(3)>"
"[Cl]-c:2:c:c:1:n:o:n:c:1:c:c:2"                                                                                                                                                                                                                                                                                                                                                                                    ,"<regId=diazox_B(3)>"
"[#6]-[#6](=[#16])-[#1]"                                                                                                                                                                                                                                                                                                                                                                                            ,"<regId=thio_aldehyd_A(3)>"
"[#6;X4]-[#7](-[#1])-[#6](-[#6]:[#6])=[#6](-[#1])-[#6](=[#16])-[#7](-[#1])-c:1:c:c:c:c:c:1"                                                                                                                                                                                                                                                                                                                         ,"<regId=thio_amide_B(2)>"
"[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#16]-[#6](-[#1])(-[#1])-c1cn(cn1)-[#1]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=imidazole_B(2)>"
"[#8]=[#6]-[#7](-[#1])-c:1:c(-[#6]:[#6]):n:c(-[#6](-[#1])(-[#1])-[#6]#[#7]):s:1"                                                                                                                                                                                                                                                                                                                                    ,"<regId=thiazole_amine_E(2)>"
"[#6](-[#1])-[#7](-[#1])-c:1:n:c(:c:s:1)-c2cnc3n2ccs3"                                                                                                                                                                                                                                                                                                                                                              ,"<regId=thiazole_amine_F(2)>"
"[#7]-,:1-,:[#6](=[#8])-,:[#6](=,:[#6](-[#6])-,:[#16]-,:[#6]-,:1=[#16])-[#1]"                                                                                                                                                                                                                                                                                                                                       ,"<regId=thio_ester_C(2)>"
"[#6](-[#16])(-[#7])=[#6](-[#1])-[#6]=[#6](-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=ene_one_B(2)>"
"[#8]=[#6]-3-c:1:c(:c:c:c:c:1)-[#6]-2=[#6](-[#8]-[#1])-[#6](=[#8])-[#7]-c:4:c-2:c-3:c:c:c:4"                                                                                                                                                                                                                                                                                                                        ,"<regId=quinone_C(2)>"
"c:1:2:c:c:c:c(:c:1:c(:c:c:c:2)-[$([#8]-[#1]),$([#7](-[#1])-[#1])])-[#6](-[#6])=[#8]"                                                                                                                                                                                                                                                                                                                               ,"<regId=keto_naphthol_A(2)>"
"[#6](-[#1])(-c:1:c:c:c:c:c:1)(-c:2:c:c:c:c:c:2)-[#6](=[#16])-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                                            ,"<regId=thio_amide_C(2)>"
"[#7]-2(-[#6](=[#8])-c:1:c(:c(:c(:c(:c:1-[#1])-[#6](=[#8])-[#8]-[#1])-[#1])-[#1])-[#6]-2=[#8])-c:3:c(:c:c(:c(:c:3)-[#1])-[#8])-[#1]"                                                                                                                                                                                                                                                                                ,"<regId=phthalimide_misc(2)>"
"c:1:c:c(:c:c:c:1-[#7](-[#1])-[#16](=[#8])=[#8])-[#7](-[#1])-[#16](=[#8])=[#8]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=sulfonamide_D(2)>"
"[#6](-[#1])-[#7](-[#1])-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#6]-[#1]"                                                                                                                                                                                                                                                                                                                         ,"<regId=anil_NH_alk_C(2)>"
"s1c(c(c-,:2c1-,:[#7](-[#1])-,:[#6](-,:[#6](=,:[#6]-,:2-[#1])-[#6](=[#8])-[#8]-[#1])=[#8])-[#7](-[#1])-[#1])-[#6](=[#8])-[#7]-[#1]"                                                                                                                                                                                                                                                                                 ,"<regId=het_65_E(2)>"
"c:2(:c:1:c(:c(:c(:c(:c:1:c(:c(:c:2-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#7](-[#1])-[#6]=[#8])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                    ,"<regId=hzide_naphth(2)>"
"[#6](-[#1])(-[#1])-c:1:c(:c(:c(:c(:c:1-[#8]-[#6](-[#1])-[#1])-[#1])-[#1])-[#6](-[#1])(-[#1])-[#7](-[#1])-[#6;X4])-[#1]"                                                                                                                                                                                                                                                                                            ,"<regId=anisol_B(2)>"
"[#6]-1=[#6]-[#7]-[#6](-[#16]-[#6;X4]-1)=[#16]"                                                                                                                                                                                                                                                                                                                                                                     ,"<regId=thio_carbam_ene(2)>"
"[#6](-[#7](-[#6]-[#1])-[#6]-[#1]):[#6]-[#7](-[#1])-[#6](=[#16])-[#6]-[#1]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=thio_amide_D(2)>"
"n2nc(c1cccc1c2-[#6])-[#6]"                                                                                                                                                                                                                                                                                                                                                                                         ,"<regId=het_65_Da(2)>"
"s:1:c(:c(-[#1]):c(:c:1-[#6](=[#8])-[#7](-[#1])-[#7]-[#1])-[#8]-[#6](-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                             ,"<regId=thiophene_D(2)>"
"[#6]-1:[#6]-[#7]=[#6]-[#6](=[#6]-[#7]-[#6])-[#16]-1"                                                                                                                                                                                                                                                                                                                                                               ,"<regId=het_thio_6_ene(2)>"
"[#6](-[#1])(-[#1])-[#6](-[#1])(-[#6]#[#7])-[#6](=[#8])-[#6]"                                                                                                                                                                                                                                                                                                                                                       ,"<regId=cyano_keto_A(2)>"
"c2(c(-[#7](-[#1])-[#1])n(-c:1:c:c:c:c:c:1-[#6](=[#8])-[#8]-[#1])nc2-[#6]=[#8])-[$([#6]#[#7]),$([#6]=[#16])]"                                                                                                                                                                                                                                                                                                       ,"<regId=anthranil_acid_C(2)>"
"c:2:c:1:c:c:c:c-,:3:c:1:c(:c:c:2)-,:[#7](-,:[#7]=,:[#6]-,:3)-[#1]"                                                                                                                                                                                                                                                                                                                                                 ,"<regId=naphth_amino_C(2)>"
"c:2:c:1:c:c:c:c-,:3:c:1:c(:c:c:2)-,:[#7]-,:[#7]=,:[#7]-,:3"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=naphth_amino_D(2)>"
"c1csc(n1)-[#7]-[#7]-[#16](=[#8])=[#8]"                                                                                                                                                                                                                                                                                                                                                                             ,"<regId=thiazole_amine_G(2)>"
"c:1:c:c:c:2:c(:c:1):n:c(:n:c:2)-[#7](-[#1])-[#6]-3=[#7]-[#6](-[#6]=[#6]-[#7]-3-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                          ,"<regId=het_66_B(2)>"
"c:1-,:3:c(:c(:c(:c(:c:1)-[#8]-[#6]-[#1])-[#1])-[#1])-,:c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-,:[#6](=[#8])-,:[#8]-,:3"                                                                                                                                                                                                                                                                      ,"<regId=coumarin_A(2)>"
"c:12:c(:c:c:c:n:1)c(c(-[#6](=[#8])~[#8;X1])s2)-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                   ,"<regId=anthranil_acid_D(2)>"
"c:1:2:n:c(:c(:n:c:1:[#6]:[#6]:[#6]:[!#1]:2)-[#6](-[#1])=[#6](-[#8]-[#1])-[#6])-[#6](-[#1])=[#6](-[#8]-[#1])-[#6]"                                                                                                                                                                                                                                                                                                  ,"<regId=het_66_C(2)>"
"c1csc(c1-[#7](-[#1])-[#1])-[#6](-[#1])=[#6](-[#1])-c2cccs2"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=thiophene_amino_E(2)>"
"c:2:c:c:1:n:c:3:c(:n:c:1:c:c:2):c:c:c:4:c:3:c:c:c:c:4"                                                                                                                                                                                                                                                                                                                                                             ,"<regId=het_6666_A(2)>"
"[#6]:[#6]-[#7](-[#1])-[#16](=[#8])(=[#8])-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                                   ,"<regId=sulfonamide_E(2)>"
"c:1:c:c(:c:c:c:1-[#7](-[#1])-[#1])-[#7](-[#6;X3])-[#6;X3]"                                                                                                                                                                                                                                                                                                                                                         ,"<regId=anil_di_alk_K(2)>"
"[#7]-2=[#6](-c:1:c:c:c:c:c:1)-[#6](-[#1])(-[#1])-[#6](-[#8]-[#1])(-[#6](-[#9])(-[#9])-[#9])-[#7]-2-[$([#6]:[#6]:[#6]:[#6]:[#6]:[#6]),$([#6](=[#16])-[#6]:[#6]:[#6]:[#6]:[#6]:[#6])]"                                                                                                                                                                                                                               ,"<regId=het_5_C(2)>"
"c:1:c(:c:c:c:c:1)-[#6](=[#8])-[#6](-[#1])=[#6]-,:3-,:[#6](=[#8])-,:[#7](-[#1])-,:[#6](=[#8])-,:[#6](=[#6](-[#1])-c:2:c:c:c:c:c:2)-,:[#7]-,:3-[#1]"                                                                                                                                                                                                                                                                 ,"<regId=ene_six_het_B(2)>"
"[#8]=[#6]-4-[#6]-[#6]-[#6]-3-[#6]-2-[#6](=[#8])-[#6]-[#6]-1-[#6]-[#6]-[#6]-[#6]-1-[#6]-2-[#6]-[#6]-[#6]-3=[#6]-4"                                                                                                                                                                                                                                                                                                  ,"<regId=steroid_A(2)>"
"c:1:2:c:3:c(:c(-[#8]-[#1]):c(:c:1:c(:c:n:2-[#6])-[#6]=[#8])-[#1]):n:c:n:3"                                                                                                                                                                                                                                                                                                                                         ,"<regId=het_565_A(2)>"
"[#6;X4]-[#7+](-[#6;X4]-[#8]-[#1])=[#6]-[#16]-[#6]-[#1]"                                                                                                                                                                                                                                                                                                                                                            ,"<regId=thio_imine_ium(2)>"
"[#6]-3(=[#8])-[#6](=[#6](-[#1])-[#7](-[#1])-c:1:c:c:c:c:c:1-[#6](=[#8])-[#8]-[#1])-[#7]=[#6](-c:2:c:c:c:c:c:2)-[#8]-3"                                                                                                                                                                                                                                                                                             ,"<regId=anthranil_acid_E(2)>"
"c:1(:c(:c(:[c;!H0,$(c-[#6;!H0;!H1])](:o:1))-[#1])-[#1])-[#6;!H0,$([#6]-[#6;!H0;!H1])]=[#7]-[#7](-[#1])-c:2:c:c:n:c:c:2"                                                                                                                                                                                                                                                                                            ,"<regId=hzone_furan_B(2)>"
"c:1(:c(:c(:[c;!H0,$(c-[#6;!H0,!H1])](:s:1))-[#1])-[#1])-[#6;!H0,$([#6]-[#6;!H0;!H1])]-[#6](=[#8])-[#7](-[#1])-c:2:n:c:c:s:2"                                                                                                                                                                                                                                                                                       ,"<regId=thiophene_E(2)>"
"[#6]:[#6]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#6]=[#8])-[#7]-2-[#6](=[#8])-[#6]-1(-[#1])-[#6](-[#1])(-[#1])-[#6]=[#6]-[#6](-[#1])(-[#1])-[#6]-1(-[#1])-[#6]-2=[#8]"                                                                                                                                                                                                                                                   ,"<regId=ene_misc_B(2)>"
"[#6]-1(-[#6]=[#8])(-[#6]:[#6])-[#16;X2]-[#6]=[#7]-[#7]-1-[#1]"                                                                                                                                                                                                                                                                                                                                                     ,"<regId=het_thio_5_B(2)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:s:1)-[#7](-[#1])-[#6](=[#8])-c:2:c:c:c:c:c:2)-[#6]#[#7])-[#6]:3:[!#1]:[!#1]:[!#1]:[!#1]:[!#1]:3"                                                                                                                                                                                                                                                                                   ,"<regId=thiophene_amino_F(2)>"
"[#6](-[#1])(-[#1])-[#8]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#1])-c:2:c:c:c:c:c:2-[$([#6](-[#1])-[#1]),$([#8]-[#6](-[#1])-[#1])]"                                                                                                                                                                                                                                     ,"<regId=anil_OC_alk_D(2)>"
"[#6](-[#1])(-[#1])(-[#1])-[#6](-[#6](-[#1])(-[#1])-[#1])(-[#6](-[#1])(-[#1])-[#1])-c:1:c(:c:c(:c(:c:1-[#1])-[#6](-[#6](-[#1])(-[#1])-[#1])(-[#6](-[#1])(-[#1])-[#1])-[#6](-[#1])(-[#1])-[#1])-[#8]-[#6](-[#1])-[#7])-[#1]"                                                                                                                                                                                         ,"<regId=tert_butyl_A(2)>"
"c:1(:c(:o:c:c:1)-[#6]-[#1])-[#6]=[#7]-[#7](-[#1])-[#6](=[#16])-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                                          ,"<regId=thio_urea_J(2)>"
"[#7](-[#1])-c1nc(nc2nnc(n12)-[#16]-[#6])-[#7](-[#1])-[#6]"                                                                                                                                                                                                                                                                                                                                                         ,"<regId=het_thio_65_B(2)>"
"c:1-,:2:c(:c:c:c:c:1-[#6](-[#1])(-[#1])-[#6](-[#1])=[#6](-[#1])-[#1])-,:[#6](=,:[#6](-[#6](=[#8])-[#7](-[#1])-[#6]:[#6])-,:[#6](=[#8])-,:[#8]-,:2)-[#1]"                                                                                                                                                                                                                                                           ,"<regId=coumarin_B(2)>"
"[#6]-2(=[#16])-[#7]-1-[#6]:[#6]-[#7]=[#7]-[#6]-1=[#7]-[#7]-2-[#1]"                                                                                                                                                                                                                                                                                                                                                 ,"<regId=thio_urea_K(2)>"
"[#6]:[#6]:[#6]:[#6]:[#6]:[#6]-c:1:c:c(:c(:s:1)-[#7](-[#1])-[#6](=[#8])-[#6])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                ,"<regId=thiophene_amino_G(2)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:c:c:1-[#7](-[#1])-[#6](-[#1])(-[#6])-[#6](-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                               ,"<regId=anil_NH_alk_D(2)>"
"[#16]=[#6]-,:2-,:[#7](-[#1])-,:[#7]=,:[#6](-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#1])-,:[#8]-,:2"                                                                                                                                                                                                                                                                                         ,"<regId=het_thio_5_C(2)>"
"[#16]=[#6]-c:1:c:c:c:2:c:c:c:c:n:1:2"                                                                                                                                                                                                                                                                                                                                                                              ,"<regId=thio_keto_het(2)>"
"[#6]~1~[#6](~[#7]~[#7]~[#6](~[#6](-[#1])-[#1])~[#6](-[#1])-[#1])~[#7]~[#16]~[#6]~1"                                                                                                                                                                                                                                                                                                                                ,"<regId=het_thio_N_5B(2)>"
"[#6]-1(-[#6]=,:[#6]-[#6]=,:[#6]-[#6]-1=[!#6&!#1])=[!#6&!#1]"                                                                                                                                                                                                                                                                                                                                                       ,"<regId=quinone_D(2)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:1:c(-[#1]):c(:c(:o:1)-[#6](-[#1])=[#6]-[#6]#[#7])-[#1]"                                                                                                                                                                                                                                                                                                               ,"<regId=anil_di_alk_furan_B(2)>"
"[#8]=[#6]-1-[#6]:[#6]-[#6](-[#1])(-[#1])-[#7]-[#6]-1=[#6]-[#1]"                                                                                                                                                                                                                                                                                                                                                    ,"<regId=ene_six_het_C(2)>"
"[#6]:[#6]-[#7]:2:[#7]:[#6]:1-[#6](-[#1])(-[#1])-[#16;X2]-[#6](-[#1])(-[#1])-[#6]:1:[#6]:2-[#7](-[#1])-[#6](=[#8])-[#6](-[#1])=[#6]-[#1]"                                                                                                                                                                                                                                                                           ,"<regId=het_55_A(2)>"
"n:1:c(:n(:c:2:c:1:c:c:c:c:2)-[#6](-[#1])-[#1])-[#16]-[#6](-[#1])(-[#1])-[#6](=[#8])-[#7](-[#1])-[#7]=[#6](-[#1])-[#6](-[#1])=[#6]-[#1]"                                                                                                                                                                                                                                                                            ,"<regId=het_thio_65_C(2)>"
"c:1(:c:c(:c(:c:c:1)-[#8]-[#1])-[#6](=!@[#6]-[#7])-[#6]=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                            ,"<regId=hydroquin_A(2)>"
"c:1(:c:c(:c(:c:c:1)-[#7](-[#1])-[#6](=[#8])-[#6]:[#6])-[#6](=[#8])-[#8]-[#1])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                           ,"<regId=anthranil_acid_F(2)>"
"n2(-[#6](-[#1])-[#1])c-1c(-[#6]:[#6]-[#6]-1=[#8])cc2-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                             ,"<regId=pyrrole_I(2)>"
"[#6](-[#1])-[#7](-[#1])-c:1:c(:c(:c(:s:1)-[#6]-[#1])-[#6]-[#1])-[#6](=[#8])-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                 ,"<regId=thiophene_amino_H(2)>"
"[#6]:[#6]-[#7;!R]=[#6]-2-[#6](=[!#6&!#1])-c:1:c:c:c:c:c:1-[#7]-2"                                                                                                                                                                                                                                                                                                                                                  ,"<regId=imine_one_fives_C(2)>"
"c:1:c:c:c:c:c:1-[#6](=[#8])-[#7](-[#1])-[#7]=[#6]-3-c:2:c:c:c:c:c:2-c:4:c:c:c:c:c-3:4"                                                                                                                                                                                                                                                                                                                             ,"<regId=keto_phenone_zone_A(2)>"
"c:1:c(:c:c:c:c:1)-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])=[#6](-[#1])-[#6]=!@[#6](-[#1])-[#6](-[#1])=[#6]-[#6]=@[#7]-c:2:c:c:c:c:c:2"                                                                                                                                                                                                                                                                                  ,"<regId=dyes7A(2)>"
"[#6]:1:2:[!#1]:[#7+](:[!#1]:[#6;!H0,$([#6]-[*])](:[!#1]:1:[#6]:[#6]:[#6]:[#6]:2))~[#6]:[#6]"                                                                                                                                                                                                                                                                                                                       ,"<regId=het_pyridiniums_B(2)>"
"[#7]-2(-c:1:c:c:c:c:c:1)-[#7]=[#6](-[#6](-[#1])-[#1])-[#6](-[#1])(-[#16]-[#6])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                                        ,"<regId=het_5_D(2)>"
"c:1:c:c:c(:c:c:1-[#7](-[#1])-c2nc(c(-[#1])s2)-c:3:c:c:c(:c:c:3)-[#6](-[#1])(-[#6]-[#1])-[#6]-[#1])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                          ,"<regId=thiazole_amine_H(1)>"
"[#6](-[#1])(-[#1])-[#7](-[#1])-[#6]=[#7]-[#7](-[#1])-c1nc(c(-[#1])s1)-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                   ,"<regId=thiazole_amine_I(1)>"
"[#6]:[#6]-[#7](-[#1])-[#6](=[#8])-c1c(snn1)-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                                 ,"<regId=het_thio_N_5C(1)>"
"[#8]=[#16](=[#8])(-[#6]:[#6])-[#7](-[#1])-c1nc(cs1)-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                                     ,"<regId=sulfonamide_F(1)>"
"[#8]=[#16](=[#8])(-[#6]:[#6])-[#7](-[#1])-[#7](-[#1])-c1nc(cs1)-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=thiazole_amine_J(1)>"
"s2c:1:n:c:n:c(:c:1c(c2-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#7]-[#7]=[#6]-c3ccco3"                                                                                                                                                                                                                                                                                                                                 ,"<regId=het_65_F(1)>"
"[#6](=[#8])-[#6](-[#1])=[#6](-[#8]-[#1])-[#6](-[#8]-[#1])=[#6](-[#1])-[#6](=[#8])-[#6]"                                                                                                                                                                                                                                                                                                                            ,"<regId=keto_keto_beta_E(1)>"
"c:2(:c:1-[#6](-[#6](-[#6](-c:1:c(:c(:c:2-[#1])-[#1])-[#1])(-[#1])-[#1])=[#8])=[#6](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                      ,"<regId=ene_five_one_B(1)>"
"[#6]:[#6]-[#7](-[#1])-[#7]=[#6](-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#6](-[#1])-[#1])=[#7]-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                          ,"<regId=keto_keto_beta_zone(1)>"
"[#6;X4]-[#16;X2]-[#6](=[#7]-[!#1]:[!#1]:[!#1]:[!#1])-[#7](-[#1])-[#7]=[#6]"                                                                                                                                                                                                                                                                                                                                        ,"<regId=thio_urea_L(1)>"
"[#6]-1(=[#7]-[#7](-[#6](-[#16]-1)=[#6](-[#1])-[#6]:[#6])-[#6]:[#6])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=het_thio_urea_ene(1)>"
"c:1(:c(:c:2:c(:n:c:1-[#7](-[#1])-[#1]):c:c:c(:c:2-[#7](-[#1])-[#1])-[#6]#[#7])-[#6]#[#7])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                               ,"<regId=cyano_amino_het_A(1)>"
"[!#1]:1:[!#1]:[!#1]:[!#1](:[!#1]:[!#1]:1)-[#6](-[#1])=[#6](-[#1])-[#6](-[#7](-[#1])-[#7](-[#1])-c2nnnn2-[#6])=[#8]"                                                                                                                                                                                                                                                                                                ,"<regId=tetrazole_hzide(1)>"
"c:1:2:c(:c(:c(:c(:c:1:c(:c(:c(:c:2-[#1])-[#1])-[#6](=[#7]-[#6]:[#6])-[#6](-[#1])-[#1])-[#8]-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                          ,"<regId=imine_naphthol_A(1)>"
"c:1(:c(:c:2:c(:c(:c:1-[#8]-[#6](-[#1])-[#1])-[#1]):c(:c(:c(:c:2-[#7](-[#1])-[#6](-[#1])(-[#1])-[#1])-[#1])-c:3:c(:c(:c(:c(:c:3-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#1])-[#1])-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                  ,"<regId=misc_anisole_A(1)>"
"c:1:c:c-2:c(:c:c:1)-[#16]-c3c(-[#7]-2)cc(s3)-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                     ,"<regId=het_thio_665(1)>"
"c:1:c:c:c-2:c(:c:1)-[#6](-[#6](-[#7]-2-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7]-4-[#6](-c:3:c:c:c:c:c:3-[#6]-4=[#8])=[#8])(-[#1])-[#1])(-[#1])-[#1]"                                                                                                                                                                                                                                                             ,"<regId=anil_di_alk_L(1)>"
"c:1(:c:c:c(:c:c:1)-[#6]-,:3=,:[#6]-,:[#6](-,:c2cocc2-,:[#6](=,:[#6]-,:3)-[#8]-[#1])=[#8])-[#16]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                  ,"<regId=colchicine_B(1)>"
"[#6;X4]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](=[#8])-[#7](-[#1])-[#6](-[#1])(-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#16]-[#6](-[#1])(-[#1])-[#1])-[#6](=[#8])-[#8]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                               ,"<regId=misc_aminoacid_A(1)>"
"n:1:c(:n(:c(:c:1-c:2:c:c:c:c:c:2)-c:3:c:c:c:c:c:3)-[#7]=!@[#6])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                  ,"<regId=imidazole_amino_A(1)>"
"[#6](-c:1:c:c:c(:c:c:1)-[#8]-[#1])(-c:2:c:c:c(:c:c:2)-[#8]-[#1])-[#8]-[#16](=[#8])=[#8]"                                                                                                                                                                                                                                                                                                                           ,"<regId=phenol_sulfite_A(1)>"
"c:2:c:c:1:n:c(:c(:n:c:1:c:c:2)-[#6](-[#1])(-[#1])-[#6](=[#8])-[#6]:[#6])-[#6](-[#1])(-[#1])-[#6](=[#8])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                 ,"<regId=het_66_D(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#1])-[#6](=[#8])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:2:c:c:c(-[#6](-[#1])-[#1])c:c:2"                                                                                                                                                                                                                                     ,"<regId=misc_anisole_B(1)>"
"[#6](-[#1])(-[#1])-c1nnnn1-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#8]-[#6](-[#1])(-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                               ,"<regId=tetrazole_A(1)>"
"[#6]-2(=[#7]-c1c(c(nn1-[#6](-[#6]-2(-[#1])-[#1])=[#8])-[#7](-[#1])-[#1])-[#7](-[#1])-[#1])-[#6]"                                                                                                                                                                                                                                                                                                                   ,"<regId=het_65_G(1)>"
"[#6](-[#6]:[#6])(-[#6]:[#6])(-[#6]:[#6])-[#16]-[#6]:[#6]-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=misc_trityl_A(1)>"
"[#8]=[#6](-c:1:c(:c(:n:c(:c:1-[#1])-[#8]-[#6](-[#1])(-[#1])-[#1])-[#8]-[#6](-[#1])(-[#1])-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                 ,"<regId=misc_pyridine_OC(1)>"
"[#7]-1=[#6](-[#7](-[#6](-[#6](-[#6]-1(-[#1])-[#6]:[#6])(-[#1])-[#1])=[#8])-[#1])-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                        ,"<regId=het_6_hydropyridone(1)>"
"[#6]-1(=[#6](-[#6](-[#6](-[#6](-[#6]-1(-[#1])-[#1])(-[#1])-[#6](=[#8])-[#6])(-[#1])-[#6](=[#8])-[#8]-[#1])(-[#1])-[#1])-[#6]:[#6])-[#6]:[#6]"                                                                                                                                                                                                                                                                      ,"<regId=misc_stilbene(1)>"
"[#6](-[#1])(-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[Cl])-[#1])-[#1])(-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[Cl])-[#1])-[#1])-[#8]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-c3nc(c(n3-[#6](-[#1])(-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                     ,"<regId=misc_imidazole(1)>"
"n:1:c(:c(:c(:c(:c:1-[#1])-[#7](-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=anil_NH_no_alk_A(1)>"
"[#7](-[#1])(-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#8]-[#1])-[#6]-2=[#6](-[#8]-[#6](-[#7]=[#7]-2)=[#7])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                   ,"<regId=het_6_imidate_B(1)>"
"[#7](-[#1])(-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                ,"<regId=anil_alk_B(1)>"
"c:1:c:c-3:c(:c:c:1)-c:2:c:c:c(:c:c:2-[#6]-3=[#6](-[#1])-[#6])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=styrene_anil_A(1)>"
"c:1:c:c-2:c(:c:c:1)-[#7](-[#6](-[#8]-[#6]-2)(-[#6](=[#8])-[#8]-[#1])-[#6](-[#1])-[#1])-[#6](=[#8])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                               ,"<regId=misc_aminal_acid(1)>"
"n:1:c(:c(:c(:c(:c:1-[#7](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#6](-[#1])-[#1])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                  ,"<regId=anil_no_alk_D(1)>"
"[#7](-[#1])(-c:1:c:c:c:c:c:1)-[#6](-[#6])(-[#6])-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                ,"<regId=anil_alk_C(1)>"
"[#7](-[#1])(-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#8]-[#6](-[#1])(-[#1])-[#1])-[#8]-[#6]-[#1])-[#1])-[#6](=[#8])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])(-[#1])-[#1])-[#6]:[#6]"                                                                                                                                                                                        ,"<regId=misc_anisole_C(1)>"
"c:1-2:c:c-3:c(:c:c:1-[#8]-[#6]-[#8]-2)-[#6]-[#6]-3"                                                                                                                                                                                                                                                                                                                                                                ,"<regId=het_465_misc(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#6](=[#8])-[#8]-[#1])-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                        ,"<regId=anthranil_acid_G(1)>"
"c:1(:c:4:c(:n:c(:c:1-[#6](-[#1])(-[#1])-[#7]-3-c:2:c(:c(:c(:c(:c:2-[#6](-[#1])(-[#1])-[#6]-3(-[#1])-[#1])-[#1])-[#1])-[#1])-[#1])-[#1]):c(:c(:c(:c:4-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                 ,"<regId=anil_di_alk_M(1)>"
"c:1:c(:c2:c(:c:c:1)c(c(n2-[#1])-[#6]:[#6])-[#6]:[#6])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                       ,"<regId=anthranil_acid_H(1)>"
"[#6]:[#6]-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-c:1:c(:c(:c(:c(:c:1-[F,Cl,Br,I])-[#1])-[#6](-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                               ,"<regId=thio_urea_M(1)>"
"n:1:c3:c(:c:c2:c:1nc(s2)-[#7])sc(n3)-[#7]"                                                                                                                                                                                                                                                                                                                                                                         ,"<regId=thiazole_amine_K(1)>"
"[#7]=[#6]-1-[#16]-[#6](=[#7])-[#7]=[#6]-1"                                                                                                                                                                                                                                                                                                                                                                         ,"<regId=het_thio_5_imine_A(1)>"
"c:1:c(:n:c:c:c:1)-[#6](=[#16])-[#7](-[#1])-c:2:c(:c:c:c:c:2)-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                ,"<regId=thio_amide_E(1)>"
"c:1-2:c(:c(:c(:c(:c:1-[#6](-c:3:c(-[#16]-[#6]-2(-[#1])-[#1]):c(:c(-[#1]):c(:c:3-[#1])-[#1])-[#1])-[#8]-[#6]:[#6])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                          ,"<regId=het_thio_676_B(1)>"
"[#6](-[#1])(-[#1])(-[#1])-c:1:c(:c(:c(:c(:n:1)-[#7](-[#1])-[#16](-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#8]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])(=[#8])=[#8])-[#1])-[#1])-[#1]"                                                                                                                                                                                                          ,"<regId=sulfonamide_G(1)>"
"[#6](=[#8])(-[#7]-1-[#6]-[#6]-[#16]-[#6]-[#6]-1)-c:2:c(:c(:c(:c(:c:2-[#16]-[#6](-[#1])-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                               ,"<regId=thio_thiomorph_Z(1)>"
"c:1:c:c:3:c:2:c(:c:1)-[#6](-[#6]=[#6](-c:2:c:c:c:3)-[#8]-[#6](-[#1])-[#1])=[#8]"                                                                                                                                                                                                                                                                                                                                   ,"<regId=naphth_ene_one_A(1)>"
"c:1-3:c:2:c(:c(:c:c:1)-[#7]):c:c:c:c:2-[#6](-[#6]=[#6]-3-[#6](-[F])(-[F])-[F])=[#8]"                                                                                                                                                                                                                                                                                                                               ,"<regId=naphth_ene_one_B(1)>"
"c:1:c:c:c:c:2:c:1:c:c:3:c(:n:2):n:c:4:c(:c:3-[#7]):c:c:c:c:4"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=amino_acridine_A(1)>"
"c:1:c-3:c(:c:c:c:1)-[#6]-2=[#7]-[!#1]=[#6]-[#6]-[#6]-2-[#6]-3=[#8]"                                                                                                                                                                                                                                                                                                                                                ,"<regId=keto_phenone_B(1)>"
"c:1-3:c(:c(:c(:c(:c:1-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#6](=[#7]-[#7](-[#1])-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#6](=[#8])-[#8]-[#1])-[#1])-[#1])-c:4:c-3:c(:c(:c(:c:4-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                             ,"<regId=hzone_acid_A(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#1])-[#7](-[#1])-[#1])-[#1])-[#1])-[#16](=[#8])(=[#8])-[#7](-[#1])-c:2:n:n:c(:c(:c:2-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                 ,"<regId=sulfonamide_H(1)>"
"c2(c(-[#1])n(-[#6](-[#1])-[#1])c:3:c(:c(:c:1n(c(c(c:1:c2:3)-[#1])-[#1])-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                      ,"<regId=het_565_indole(1)>"
"c1(c-2c(c(n1-[#6](-[#8])=[#8])-[#6](-[#1])-[#1])-[#16]-[#6](-[#1])(-[#1])-[#16]-2)-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                               ,"<regId=pyrrole_J(1)>"
"s1ccnc1-c2c(n(nc2-[#1])-[#1])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                                    ,"<regId=pyrazole_amino_B(1)>"
"c1(c(c(c(n1-[#1])-c:2:c(:c(:c(:c(:c:2-[#1])-[#1])-[#1])-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                 ,"<regId=pyrrole_K(1)>"
"c:1:2(:c(:c(:c(:o:1)-[#6])-[#1])-[#1])-[#6](=[#8])-[#7](-[#1])-[#6]:[#6](-[#1]):[#6](-[#1]):[#6](-[#1]):[#6](-[#1]):[#6]:2-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                  ,"<regId=anthranil_acid_I(1)>"
"[!#1]:[#6]-[#6](=[#16])-[#7](-[#1])-[#7](-[#1])-[#6]:[!#1]"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=thio_amide_F(1)>"
"[#6]-1(=[#8])-[#6](-[#6](-[#6]#[#7])=[#6](-[#1])-[#7])-[#6](-[#7])-[#6]=[#6]-1"                                                                                                                                                                                                                                                                                                                                    ,"<regId=ene_one_C(1)>"
"c2(c-1n(-[#6](-[#6]=[#6]-[#7]-1)=[#8])nc2-c3cccn3)-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=het_65_H(1)>"
"[#8]=[#6]-1-[#6](=[#7]-[#7]-[#6]-[#6]-1)-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                                                ,"<regId=cyano_imine_D(1)>"
"c:2(:c:1:c:c:c:c:c:1:n:n:c:2)-[#6](-[#6]:[#6])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                                          ,"<regId=cyano_misc_A(1)>"
"c:1:c:c-2:c(:c:c:1)-[#6]=[#6]-[#6](-[#7]-2-[#6](=[#8])-[#7](-[#1])-c:3:c:c(:c(:c:c:3)-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                           ,"<regId=ene_misc_C(1)>"
"c:2:c:c:1:n:c(:c(:n:c:1:c:c:2)-c:3:c:c:c:c:c:3)-c:4:c:c:c:c:c:4-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=het_66_E(1)>"
"[#6](-[#1])(-[#1])-[#6](-[#8]-[#1])=[#6](-[#6](=[#8])-[#6](-[#1])-[#1])-[#6](-[#1])-[#6]#[#6]"                                                                                                                                                                                                                                                                                                                     ,"<regId=keto_keto_beta_F(1)>"
"c:1:c:4:c(:c:c2:c:1nc(n2-[#1])-[#6]-[#8]-[#6](=[#8])-c:3:c:c(:c:c(:c:3)-[#7](-[#1])-[#1])-[#7](-[#1])-[#1]):c:c:c:c:4"                                                                                                                                                                                                                                                                                             ,"<regId=misc_naphthimidazole(1)>"
"c:2(:c:1:c:c:c:c-3:c:1:c(:c:c:2)-[#6]=[#6]-[#6]-3=[#7])-[#7]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=naphth_ene_one_C(1)>"
"c:2(:c:1:c:c:c:c:c:1:c-3:c(:c:2)-[#6](-c:4:c:c:c:c:c-3:4)=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=keto_phenone_C(1)>"
"[#6]-,:2(-,:[#6]=,:[#7]-,:c:1:c:c(:c:c:c:1-,:[#8]-,:2)-[Cl])=[#8]"                                                                                                                                                                                                                                                                                                                                                 ,"<regId=coumarin_C(1)>"
"[#6]-1=[#6]-[#7](-[#6](-c:2:c-1:c:c:c:c:2)(-[#6]#[#7])-[#6](=[#16])-[#16])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                              ,"<regId=thio_est_cyano_A(1)>"
"c2(nc:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])n2-[#6])-[#7](-[#1])-[#6](-[#7](-[#1])-c:3:c(:c:c:c:c:3-[#1])-[#1])=[#8]"                                                                                                                                                                                                                                                                                           ,"<regId=het_65_imidazole(1)>"
"[#7](-[#1])(-[#6]:[#6])-c:1:c(-[#6](=[#8])-[#8]-[#1]):c:c:c(:n:1)-,:[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=anthranil_acid_J(1)>"
"c:1-3:c(:c:c:c:c:1)-[#16]-[#6](=[#7]-[#7]=[#6]-2-[#6]=[#6]-[#6]=[#6]-[#6]=[#6]-2)-[#7]-3-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                         ,"<regId=colchicine_het(1)>"
"c:1-2:c(:c(:c(:c(:c:1-[#1])-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#6](=[#6](-[#6])-[#16]-[#6]-2(-[#1])-[#1])-[#6]"                                                                                                                                                                                                                                                                                  ,"<regId=ene_misc_D(1)>"
"c:12:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])c(c(-[#6]:[#6])n2-!@[#6]:[#6])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                       ,"<regId=indole_3yl_alk_B(1)>"
"[#7](-[#1])(-[#1])-c:1:c:c:c(:c:c:1-[#8]-[#1])-[#16](=[#8])(=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                      ,"<regId=anil_OH_no_alk_A(1)>"
"s:1:c:c:c(:c:1-[#1])-c:2:c:s:c(:n:2)-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                             ,"<regId=thiazole_amine_L(1)>"
"c1c(-[#7](-[#1])-[#1])nnc1-c2c(-[#6](-[#1])-[#1])oc(c2-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                 ,"<regId=pyrazole_amino_A(1)>"
"n1nscc1-c2nc(no2)-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                                                                       ,"<regId=het_thio_N_5D(1)>"
"c:1(:c:c-3:c(:c:c:1)-[#7]-[#6]-4-c:2:c:c:c:c:c:2-[#6]-[#6]-3-4)-[#6;X4]"                                                                                                                                                                                                                                                                                                                                           ,"<regId=anil_alk_indane(1)>"
"c:1-2:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#6](=[#6](-[#1])-[#6]-3-[#6](-[#6]#[#7])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#7]-2-3)-[#1]"                                                                                                                                                                                                                                                                             ,"<regId=anil_di_alk_N(1)>"
"c:2-,:3:c(:c:c:1:c:c:c:c:c:1:c:2)-,:[#7](-[#6](-[#1])-[#1])-,:[#6](=[#8])-,:[#6](=,:[#7]-,:3)-[#6]:[#6]-[#7](-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                              ,"<regId=het_666_C(1)>"
"[#6](-[#8]-[#1]):[#6]-[#6](=[#8])-[#6](-[#1])=[#6](-[#6])-[#6]"                                                                                                                                                                                                                                                                                                                                                    ,"<regId=ene_one_D(1)>"
"c:1:2:c(:c(:c(:c(:c:1-[#1])-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1]):c(:c(-[#1]):n:2-[#1])-[#16](=[#8])=[#8]"                                                                                                                                                                                                                                                                                         ,"<regId=anil_di_alk_indol(1)>"
"c:1:2:c(:c(:c(:c(:c:1-[#1])-[#1])-[#7](-[#1])-[#1])-[#1]):c(:c(-[#1]):n:2-[#6](-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                  ,"<regId=anil_no_alk_indol_A(1)>"
"[#16;X2]-1-[#6]=[#6](-[#6]#[#7])-[#6](-[#6])(-[#6]=[#8])-[#6](=[#6]-1-[#7](-[#1])-[#1])-[$([#6]=[#8]),$([#6]#[#7])]"                                                                                                                                                                                                                                                                                               ,"<regId=dhp_amino_CN_G(1)>"
"[#7]-2-[#6]=[#6](-[#6]=[#8])-[#6](-c:1:c:c:c(:c:c:1)-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#6]~3=,:[#6]-2~[#7]~[#6](~[#16])~[#7]~[#6]~3~[#7]"                                                                                                                                                                                                                                                                 ,"<regId=anil_di_alk_dhp(1)>"
"c:1:c(:c:c:c:c:1)-[#6](=[#8])-[#7](-[#1])-c:2:c(:c:c:c:c:2)-[#6](=[#8])-[#7](-[#1])-[#7](-[#1])-c:3:n:c:c:s:3"                                                                                                                                                                                                                                                                                                     ,"<regId=anthranil_amide_A(1)>"
"c:1:c:2:c(:c:c:c:1):c(:c:3:c(:c:2):c:c:c:c:3)-[#6]=[#7]-[#7](-[#1])-c:4:c:c:c:c:c:4"                                                                                                                                                                                                                                                                                                                               ,"<regId=hzone_anthran_Z(1)>"
"c:1:c(:c:c:c:c:1)-[#6](-[#1])-[#7]-[#6](=[#8])-[#6](-[#7](-[#1])-[#6](-[#1])-[#1])=[#6](-[#1])-[#6](=[#8])-c:2:c:c:c(:c:c:2)-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                ,"<regId=ene_one_amide_A(1)>"
"s:1:c(:c(-[#1]):c(:c:1-[#6]-3=[#7]-c:2:c:c:c:c:c:2-[#6](=[#7]-[#7]-3-[#1])-c:4:c:c:n:c:c:4)-[#1])-[#1]"                                                                                                                                                                                                                                                                                                            ,"<regId=het_76_A(1)>"
"o:1:c(:c(-[#1]):c(:c:1-[#6](-[#1])(-[#1])-[#7](-[#1])-[#6](=[#16])-[#7](-[#6]-[#1])-[#6](-[#1])(-[#1])-c:2:c:c:c:c:c:2)-[#1])-[#1]"                                                                                                                                                                                                                                                                                ,"<regId=thio_urea_N(1)>"
"c:1:c(:c:c:c:c:1)-[#7](-[#6]-[#1])-[#6](-[#1])-[#6](-[#1])-[#6](-[#1])-[#7](-[#1])-[#6](=[#8])-[#6]-,:2=,:[#6](-,:[#8]-,:[#6](-,:[#6](=,:[#6]-,:2-[#6](-[#1])-[#1])-[#1])=[#8])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                  ,"<regId=anil_di_alk_coum(1)>"
"c2-3:c:c:c:1:c:c:c:c:c:1:c2-[#6](-[#1])-[#6;X4]-[#7]-[#6]-3=[#6](-[#1])-[#6](=[#8])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                      ,"<regId=ene_one_amide_B(1)>"
"c:1:c(:c:c:c:c:1)-[#6]-4=[#7]-[#7]:2:[#6](:[#7+]:c:3:c:2:c:c:c:c:3)-[#16]-[#6;X4]-4"                                                                                                                                                                                                                                                                                                                               ,"<regId=het_thio_656c(1)>"
"[#6]-2(=[#8])-[#6](=[#6](-[#6](-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1])-[#7]=[#6](-c:1:c:c:c:c:c:1)-[#8]-2"                                                                                                                                                                                                                                                                ,"<regId=het_5_ene(1)>"
"c:1:c(:c:c:c:c:1)-[#7]-2-[#6](=[#8])-[#6](=[#6](-[#1])-[#6]-2=[#8])-[#16]-c:3:c:c:c:c:c:3"                                                                                                                                                                                                                                                                                                                         ,"<regId=thio_imide_A(1)>"
"[#7]-,:1(-[#1])-,:[#7]=,:[#6](-[#7]-[#1])-,:[#16]-,:[#6](=,:[#6]-,:1-,:[#6]:[#6])-,:[#6]:[#6]"                                                                                                                                                                                                                                                                                                                     ,"<regId=dhp_amidine_A(1)>"
"c:1(:c(:c-3:c(:c(:c:1-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-[#6](-[#1])-c:2:c(:c(:c(:o:2)-[#6]-[#1])-[#1])-[#1])-[#1])-[#8]-[#6](-[#8]-3)(-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                  ,"<regId=thio_urea_O(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-c:2:c:c:c:c:c:2)-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                       ,"<regId=anil_di_alk_O(1)>"
"[#8]=[#6]-!@n:1:c:c:c-,:2:c:1-,:[#7](-[#1])-,:[#6](=[#16])-,:[#7]-,:2-[#1]"                                                                                                                                                                                                                                                                                                                                        ,"<regId=thio_urea_P(1)>"
"[#6](-[F])(-[F])-[#6](=[#8])-[#7](-[#1])-c:1:c(-[#1]):n(-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#8]-[#6](-[#1])(-[#1])-[#6]:[#6]):n:c:1-[#1]"                                                                                                                                                                                                                                                                      ,"<regId=het_pyraz_misc(1)>"
"[#7]-2=[#7]-[#6]:1:[#7]:[!#6&!#1]:[#7]:[#6]:1-[#7]=[#7]-[#6]:[#6]-2"                                                                                                                                                                                                                                                                                                                                               ,"<regId=diazox_C(1)>"
"[#6]-2(-[#1])(-[#8]-[#1])-[#6]:1:[#7]:[!#6&!#1]:[#7]:[#6]:1-[#6](-[#1])(-[#8]-[#1])-[#6]=[#6]-2"                                                                                                                                                                                                                                                                                                                   ,"<regId=diazox_D(1)>"
"[#6]-1(-[#6](-[#1])(-[#1])-[#6]-1(-[#1])-[#1])(-[#6](=[#8])-[#7](-[#1])-c:2:c:c:c(:c:c:2)-[#8]-[#6](-[#1])(-[#1])-[#8])-[#16](=[#8])(=[#8])-[#6]:[#6]"                                                                                                                                                                                                                                                             ,"<regId=misc_cyclopropane(1)>"
"[#6]-1:[#6]-[#6](=[#8])-[#6]=[#6]-1-[#7]=[#6](-[#1])-[#7](-[#6;X4])-[#6;X4]"                                                                                                                                                                                                                                                                                                                                       ,"<regId=imine_ene_one_B(1)>"
"c:1:c:c(:c:c-,:2:c:1-,:[#6](=,:[#6](-[#1])-,:[#6](=[#8])-,:[#8]-,:2)-c:3:c:c:c:c:c:3)-[#8]-[#6](-[#1])(-[#1])-[#6]:[#8]:[#6]"                                                                                                                                                                                                                                                                                      ,"<regId=coumarin_D(1)>"
"c:1:c(:o:c(:c:1-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#7]-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#8]-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#8]-c:2:c:c-3:c(:c:c:2)-[#8]-[#6](-[#8]-3)(-[#1])-[#1]"                                                                                                                                                                                                     ,"<regId=misc_furan_A(1)>"
"[#7]-4(-c:1:c:c:c:c:c:1)-[#6](=[#8])-[#16]-[#6](-[#1])(-[#7](-[#1])-c:2:c:c:c:c:3:c:c:c:c:c:2:3)-[#6]-4=[#8]"                                                                                                                                                                                                                                                                                                      ,"<regId=rhod_sat_E(1)>"
"[#7]-3(-[#6](=[#8])-c:1:c:c:c:c:c:1)-[#6](=[#7]-c:2:c:c:c:c:c:2)-[#16]-[#6](-[#1])(-[#1])-[#6]-3=[#8]"                                                                                                                                                                                                                                                                                                             ,"<regId=rhod_sat_imine_A(1)>"
"[#7]-2(-c:1:c:c:c:c:c:1)-[#6](=[#8])-[#16]-[#6](-[#1])(-[#1])-[#6]-2=[#16]"                                                                                                                                                                                                                                                                                                                                        ,"<regId=rhod_sat_F(1)>"
"[#7]-1(-[#6](-[#1])-[#1])-[#6](=[#16])-[#7](-[#6]:[#6])-[#6](=[#7]-[#6]:[#6])-[#6]-1=[#7]-[#6]:[#6]"                                                                                                                                                                                                                                                                                                               ,"<regId=het_thio_5_imine_B(1)>"
"[#16]-1-[#6](=[#7]-[#7]-[#1])-[#16]-[#6](=[#7]-[#6]:[#6])-[#6]-1=[#7]-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                   ,"<regId=het_thio_5_imine_C(1)>"
"[#6]-2(=[#8])-[#6](=[#6](-[#1])-c:1:c(:c:c:c(:c:1)-[F,Cl,Br,I])-[#8]-[#6](-[#1])-[#1])-[#7]=[#6](-[#16]-[#6](-[#1])-[#1])-[#16]-2"                                                                                                                                                                                                                                                                                 ,"<regId=ene_five_het_N(1)>"
"[#6](-[#1])(-[#1])-[#16]-[#6](=[#16])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=thio_carbam_A(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](-[#1])-[#1])-[#7](-[#1])-[#6](=[#8])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6]:[#6])-[#1])-[#7](-[#1])-[#6](=[#8])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6]:[#6]"                                                                                                                                                                                                         ,"<regId=misc_anilide_A(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#6](-[#1])-[#1])-[#1])-[Br])-[#1])-[#6](-[#1])-[#1])-[#7](-[#1])-[#6](=[#8])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                             ,"<regId=misc_anilide_B(1)>"
"c:1-2:c(:c:c:c(:c:1-[#8]-[#6](-[#1])(-[#1])-[#7](-[#6]:[#6]-[#8]-[#6](-[#1])-[#1])-[#6]-2(-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                 ,"<regId=mannich_B(1)>"
"c:1-2:c(:c(:c(:c(:c:1-[#8]-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6]-2(-[#1])-[#1])-[#1])-[#8])-[#8])-[#1]"                                                                                                                                                                                                                                                                                                  ,"<regId=mannich_catechol_A(1)>"
"[#7](-[#1])(-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                          ,"<regId=anil_alk_D(1)>"
"n:1:2:c:c:c(:c:c:1:c:c(:c:2-[#6](=[#8])-[#6]:[#6])-[#6]:[#6])-[#6](~[#8])~[#8]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=het_65_I(1)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#6](=[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1])-[#6](-[#6;X4])(-[#6;X4])-[#7](-[#1])-[#6](=[#8])-[#7](-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#6](-[#1])(-[#1])-[#6]:[#6]"                                                                                                                                                 ,"<regId=misc_urea_A(1)>"
"[#6]-3(-[#1])(-n:1:c(:n:c(:c:1-[#1])-[#1])-[#1])-c:2:c(:c(:c(:c(:c:2-[#1])-[Br])-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-c:4:c-3:c(:c(:c(:c:4-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                    ,"<regId=imidazole_C(1)>"
"[#6](=[#6](-[#1])-[#6](-[#1])(-[#1])-n:1:c(:n:c(:c:1-[#1])-[#1])-[#1])(-[#6]:[#6])-[#6]:[#6]"                                                                                                                                                                                                                                                                                                                      ,"<regId=styrene_imidazole_A(1)>"
"c:1(:n:c(:c(-[#1]):s:1)-c:2:c:c:n:c:c:2)-[#7](-[#1])-[#6]:[#6]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                   ,"<regId=thiazole_amine_M(1)>"
"c:1(:n:c(:c(-[#1]):s:1)-c:2:c:c:c:c:c:2)-[#6](-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7]-[#6](-[#1])(-[#1])-c:3:c:c:c:n:3-[#1]"                                                                                                                                                                                                                                                          ,"<regId=misc_pyrrole_thiaz(1)>"
"n:1(-[#1]):c(:c(-[#6](-[#1])-[#1]):c(:c:1-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1])-[#6](=[#8])-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                             ,"<regId=pyrrole_L(1)>"
"c:2(:n:c:1:c(:c(:c:c(:c:1-[#1])-[F,Cl,Br,I])-[#1]):n:2-[#1])-[#16]-[#6](-[#1])(-[#1])-[#6](=[#8])-[#7](-[#1])-[#6]:[#6]"                                                                                                                                                                                                                                                                                           ,"<regId=het_thio_65_D(1)>"
"c:1(:c(:c-2:c(:c(:c:1-[#8]-[#6](-[#1])-[#1])-[#1])-[#6]=[#6]-[#6](-[#1])-[#16]-2)-[#1])-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                     ,"<regId=ene_misc_E(1)>"
"[#7]-1(-[#1])-[#6](=[#16])-[#6](-[#1])(-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-[#6](=[#6]-1-[#6]:[#6])-[#1]"                                                                                                                                                                                                                                                                                                           ,"<regId=thio_cyano_A(1)>"
"n:1:c(:c(:c(:c(:c:1-[#16;X2]-c:2:c:c:c:c:c:2-[#7](-[#1])-[#1])-[#6]#[#7])-c:3:c:c:c:c:c:3)-[#6]#[#7])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                            ,"<regId=cyano_amino_het_B(1)>"
"[#7]-,:2(-c:1:c:c:c(:c:c:1)-[#8]-[#6](-[#1])-[#1])-,:[#6](=[#8])-,:[#6](=,:[#6]-,:[#6](=,:[#7]-,:2)-n:3:c:n:c:c:3)-[#6]#[#7]"                                                                                                                                                                                                                                                                                      ,"<regId=cyano_pyridone_G(1)>"
"o:1:c(:c:c:2:c:1:c(:c(:c(:c:2-[#1])-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#6](~[#8])~[#8]"                                                                                                                                                                                                                                                                                                          ,"<regId=het_65_J(1)>"
"[#6]#[#6]-[#6](=[#8])-[#6]#[#6]"                                                                                                                                                                                                                                                                                                                                                                                   ,"<regId=ene_one_yne_A(1)>"
"c:2(:c:1:c(:c(:c(:c(:c:1:c(:c(:c:2-[#8]-[#1])-[#6]=[#8])-[#1])-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                           ,"<regId=anil_OH_no_alk_B(1)>"
"c:1(:c(:c(:[c;!H0,$(c-[#6;!H0;!H1])](:o:1))-[#1])-[#1])-[#6](=[#8])-[#7](-[#1])-[#7]=[#6;!H0,$([#6]-[#6;!H0!H1])]-c:2:c:c:c:c(:c:2)-[*]-[*]-[*]-c:3:c:c:c:o:3"                                                                                                                                                                                                                                                     ,"<regId=hzone_acyl_misc_A(1)>"
"[#16](=[#8])(=[#8])-[#7](-[#1])-c:1:c(:c(:c(:s:1)-[#6]-[#1])-[#6]-[#1])-[#6](=[#8])-[#7]-[#1]"                                                                                                                                                                                                                                                                                                                     ,"<regId=thiophene_F(1)>"
"[#6](-[#1])(-[#1])-[#8]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#8]-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                       ,"<regId=anil_OC_alk_E(1)>"
"[#6](-[#1])(-[#1])-[#8]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-[#6](-[#1])(-[#6]=[#8])-[#16]"                                                                                                                                                                                                                                                                                                     ,"<regId=anil_OC_alk_F(1)>"
"n1nnnc2cccc12"                                                                                                                                                                                                                                                                                                                                                                                                     ,"<regId=het_65_K(1)>"
"c:1-,:2:c(-[#1]):s:c(:c:1-,:[#6](=[#8])-,:[#7]-,:[#7]=,:[#6]-,:2-[#7](-[#1])-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                      ,"<regId=het_65_L(1)>"
"c:1-,:3:c(:c:2:c(:c:c:1-[Br]):o:c:c:2)-,:[#6](=,:[#6]-,:[#6](=[#8])-,:[#8]-,:3)-[#1]"                                                                                                                                                                                                                                                                                                                              ,"<regId=coumarin_E(1)>"
"c:1-,:3:c(:c:c:c:c:1)-,:[#6](=,:[#6](-[#6](=[#8])-[#7](-[#1])-c:2:n:o:c:c:2-[Br])-,:[#6](=[#8])-,:[#8]-,:3)-[#1]"                                                                                                                                                                                                                                                                                                  ,"<regId=coumarin_F(1)>"
"c:1-,:2:c(:c:c(:c:c:1-[F,Cl,Br,I])-[F,Cl,Br,I])-,:[#6](=,:[#6](-[#6](=[#8])-[#7](-[#1])-[#1])-,:[#6](=[#7]-[#1])-,:[#8]-,:2)-[#1]"                                                                                                                                                                                                                                                                                 ,"<regId=coumarin_G(1)>"
"c:1-,:3:c(:c:c:c:c:1)-,:[#6](=,:[#6](-[#6](=[#8])-[#7](-[#1])-c:2:n:c(:c:s:2)-[#6]:[#16]:[#6]-[#1])-,:[#6](=[#8])-,:[#8]-,:3)-[#1]"                                                                                                                                                                                                                                                                                ,"<regId=coumarin_H(1)>"
"[#6](-[#1])(-[#1])-[#16;X2]-c:2:n:n:c:1-[#6]:[#6]-[#7]=[#6]-[#8]-c:1:n:2"                                                                                                                                                                                                                                                                                                                                          ,"<regId=het_thio_67_A(1)>"
"[#16](=[#8])(=[#8])(-c:1:c:n(-[#6](-[#1])-[#1]):c:n:1)-[#7](-[#1])-c:2:c:n(:n:c:2)-[#6](-[#1])(-[#1])-[#6]:[#6]-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                             ,"<regId=sulfonamide_I(1)>"
"c:1-2:c(:c(:c(:c(:c:1-[#8]-[#6](-[#1])(-[#1])-[#8]-2)-[#6](-[#1])(-[#1])-[#7]-3-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#6]:[#6]-3)-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                               ,"<regId=het_65_mannich(1)>"
"[#6](-[#1])(-[#1])-[#8]-[#6]:[#6]-[#6](-[#1])(-[#1])-[#7](-[#1])-c:2:c(:c(:c:1:n(:c(:n:c:1:c:2-[#1])-[#1])-[#6]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                  ,"<regId=anil_alk_A(1)>"
"[#7]-4(-c:1:c:c:c:c:c:1)-[#6](=[#7+](-c:2:c:c:c:c:c:2)-[#6](=[#7]-c:3:c:c:c:c:c:3)-[#7]-4)-[#1]"                                                                                                                                                                                                                                                                                                                   ,"<regId=het_5_inium(1)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:2:c:c:c:1:s:c(:n:c:1:c:2)-[#16]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                     ,"<regId=anil_di_alk_P(1)>"
"c:1:2:c(:c(:c(:c(:c:1:c(:c(-[#1]):c(:c:2-[#1])-[#1])-[#6](-[#6](-[#1])-[#1])=[#7]-[#7](-[#1])-[#6](=[#16])-[#7](-[#1])-[#6]:[#6]:[#6])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                     ,"<regId=thio_urea_Q(1)>"
"[#6]:1(:[#7]:[#6](:[#7]:[!#1]:[#7]:1)-c:2:c(:c(:c(:o:2)-[#1])-[#1])-[#1])-[#16]-[#6;X4]"                                                                                                                                                                                                                                                                                                                           ,"<regId=thio_pyridine_A(1)>"
"n:1:c(:n:c(:n:c:1-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#7](-[#6]-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                  ,"<regId=melamine_B(1)>"
"c:1(:n:s:c(:n:1)-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7]-[#6](=[#8])-c:2:c:c:c:c:c:2-[#6](=[#8])-[#8]-[#1])-c:3:c:c:c:c:c:3"                                                                                                                                                                                                                                                            ,"<regId=misc_phthal_thio_N(1)>"
"n:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#6](=[#8])-[#7](-[#1])-[#7]=[#6](-[#1])-c:2:c:c:c:c:c:2-[#8]-[#6](-[#1])(-[#1])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                ,"<regId=hzone_acyl_misc_B(1)>"
"[#6](-[#1])(-[#1])(-[#1])-[#6](-[#6](-[#1])(-[#1])-[#1])(-[#6](-[#1])(-[#1])-[#1])-c:1:c(:c(:c(:c(:c:1-[#8]-[#1])-[#6](-[#6](-[#1])(-[#1])-[#1])(-[#6](-[#1])(-[#1])-[#1])-[#6](-[#1])(-[#1])-[#1])-[#1])-[#6](-[#1])(-[#1])-c:2:c:c:c(:c(:c:2-[#1])-[#1])-[#8]-[#1])-[#1]"                                                                                                                                        ,"<regId=tert_butyl_B(1)>"
"[#7](-[#1])(-[#1])-c:1:c(-[#7](-[#1])-[#1]):c(:c(-[#1]):c:2:n:o:n:c:1:2)-[#1]"                                                                                                                                                                                                                                                                                                                                     ,"<regId=diazox_E(1)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:c(:c:1-[#7](-[#1])-[#16](=[#8])=[#8])-[#1])-[#7](-[#1])-[#6](-[#1])-[#1])-[F,Cl,Br,I])-[#1]"                                                                                                                                                                                                                                                                                       ,"<regId=anil_NH_no_alk_B(1)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:c(:c:1-[#7]=[#6]-2-[#6](=[#6]~[#6]~[#6]=[#6]-2)-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                      ,"<regId=anil_no_alk_A(1)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:c(:c:1-n:2:c:c:c:c:2)-[#1])-[#6](-[#1])-[#1])-[#6](-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                              ,"<regId=anil_no_alk_B(1)>"
"[#16]=[#6]-[#6](-[#6](-[#1])-[#1])=[#6](-[#6](-[#1])-[#1])-[#7](-[#6](-[#1])-[#1])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                               ,"<regId=thio_ene_amine_A(1)>"
"[#6]-1:[#6]-[#8]-[#6]-2-[#6](-[#1])(-[#1])-[#6](=[#8])-[#8]-[#6]-1-2"                                                                                                                                                                                                                                                                                                                                              ,"<regId=het_55_B(1)>"
"[#8]-[#6](=[#8])-[#6](-[#1])(-[#1])-[#16;X2]-[#6](=[#7]-[#6]#[#7])-[#7](-[#1])-c:1:c:c:c:c:c:1"                                                                                                                                                                                                                                                                                                                    ,"<regId=cyanamide_A(1)>"
"[#8]=[#6]-[#6]-1=[#6](-[#16]-[#6](=[#6](-[#1])-[#6])-[#16]-1)-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                                           ,"<regId=ene_one_one_A(1)>"
"[#8]=[#6]-1-[#7]-[#7]-[#6](=[#7]-[#6]-1=[#6]-[#1])-[!#1]:[!#1]"                                                                                                                                                                                                                                                                                                                                                    ,"<regId=ene_six_het_D(1)>"
"[#8]=[#6]-[#6](-[#1])=[#6](-[#6]#[#7])-[#6]"                                                                                                                                                                                                                                                                                                                                                                       ,"<regId=ene_cyano_E(1)>"
"[#8](-[#1])-[#6](=[#8])-c:1:c(:c(:c(:c(:c:1-[#8]-[#1])-[#1])-c:2:c(-[#1]):c(:c(:o:2)-[#6](-[#1])=[#6](-[#6]#[#7])-c:3:n:c:c:n:3)-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                 ,"<regId=ene_cyano_F(1)>"
"c:1:c(:c:c:c:c:1)-[#7](-c:2:c:c:c:c:c:2)-[#7]=[#6](-[#1])-[#6]:3:[#6](:[#6](:[#6](:[!#1]:3)-c:4:c:c:c:c(:c:4)-[#6](=[#8])-[#8]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                   ,"<regId=hzone_furan_C(1)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-c:2:c(-[#1]):c(:c(-[#6](-[#1])-[#1]):o:2)-[#6]=[#8])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                ,"<regId=anil_no_alk_C(1)>"
"[#8](-[#1])-[#6](=[#8])-c:1:c:c:c(:c:c:1)-[#7]-[#7]=[#6](-[#1])-[#6]:2:[#6](:[#6](:[#6](:[!#1]:2)-c:3:c:c:c:c:c:3)-[#1])-[#1]"                                                                                                                                                                                                                                                                                     ,"<regId=hzone_acid_D(1)>"
"[#8](-[#1])-[#6](=[#8])-c:1:c:c:c:c(:c:1)-[#6]:[!#1]:[#6]-[#6]=[#7]-[#7](-[#1])-[#6](=[#8])-[#6](-[#1])(-[#1])-[#8]"                                                                                                                                                                                                                                                                                               ,"<regId=hzone_furan_E(1)>"
"[#8](-[#1])-[#6]:1:[#6](:[#6]:[!#1]:[#6](:[#7]:1)-[#7](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6](=[#8])-[#8]"                                                                                                                                                                                                                                                                                                           ,"<regId=het_6_pyridone_NH2(1)>"
"[#6]-1(=[!#6&!#1])-[#6](-[#7]=[#6]-[#16]-1)=[#8]"                                                                                                                                                                                                                                                                                                                                                                  ,"<regId=imine_one_fives_D(1)>"
"n2(-c:1:c:c:c:c:c:1)c(c(-[#1])c(c2-[#6]=[#7]-[#8]-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                ,"<regId=pyrrole_M(1)>"
"n2(-[#6](-[#1])-c:1:c(:c(:c:c(:c:1-[#1])-[#1])-[#1])-[#1])c(c(-[#1])c(c2-[#6]-[#1])-[#1])-[#6]-[#1]"                                                                                                                                                                                                                                                                                                               ,"<regId=pyrrole_N(1)>"
"n1(-[#6](-[#1])-[#1])c(c(-[#6](=[#8])-[#6])c(c1-[#6]:[#6])-[#6])-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                 ,"<regId=pyrrole_O(1)>"
"n1(-[#6])c(c(-[#1])c(c1-[#6](-[#1])=[#6](-[#6]#[#7])-c:2:n:c:c:s:2)-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                    ,"<regId=ene_cyano_G(1)>"
"n3(-c:1:c:c:c:c:c:1-[#7](-[#1])-[#16](=[#8])(=[#8])-c:2:c:c:c:s:2)c(c(-[#1])c(c3-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                 ,"<regId=sulfonamide_J(1)>"
"n2(-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#6](=[#8])-[#7](-[#1])-[#6](-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#8]-[#6]:[#6])c(c(-[#1])c(c2-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                          ,"<regId=misc_pyrrole_benz(1)>"
"c:1(:c:c:c:c:c:1)-[#7](-[#1])-[#6](=[#16])-[#7]-[#7](-[#1])-[#6](-[#1])=[#6](-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                     ,"<regId=thio_urea_R(1)>"
"[#6]-1(-[#6](=[#8])-[#6](-[#1])(-[#1])-[#6]-[#6](-[#1])(-[#1])-[#6]-1=[#8])=[#6](-[#7]-[#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                            ,"<regId=ene_one_one_B(1)>"
"[#7](-[#1])(-[#1])-[#6]-1=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-[#16]-[#6;X4]-[#16]-1"                                                                                                                                                                                                                                                                                                                          ,"<regId=dhp_amino_CN_H(1)>"
"[#6](-[#1])(-[#1])-[#8]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#1])-[#1])-[#7](-[#1])-c:2:c:c:n:c:3:c(:c:c:c(:c:2:3)-[#8]-[#6](-[#1])-[#1])-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                       ,"<regId=het_66_anisole(1)>"
"[#6](-[#1])(-[#1])-[#8]-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#8]-[#6](-[#1])-[#1])-[#1])-[#7](-[#1])-c:2:n:c(:c:s:2)-c:3:c:c:c(:c:c:3)-[#8]-[#6](-[#1])-[#1]"                                                                                                                                                                                                                                                          ,"<regId=thiazole_amine_N(1)>"
"[#6]~1~3~[#7](-[#6]:[#6])~[#6]~[#6]~[#6]~[#6]~1~[#6]~2~[#7]~[#6]~[#6]~[#6]~[#7+]~2~[#7]~3"                                                                                                                                                                                                                                                                                                                         ,"<regId=het_pyridiniums_C(1)>"
"[#7]-3(-c:2:c:1:c:c:c:c:c:1:c:c:c:2)-[#7]=[#6](-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#6]-3=[#8]"                                                                                                                                                                                                                                                                                                                  ,"<regId=het_5_E(1)>"
"[#6]-1(=[#6;!H0,$([#6]-[#6;!H0;!H1]),$([#6]-[#6]=[#8])]-[#16]-[#6](-[#7;!H0,$([#7]-[#6;!H0]),$([#7]-[#6]:[#6])]-1)=[#7;!R])-[$([#6](-[#1])-[#1]),$([#6]:[#6])]"                                                                                                                                                                                                                                                    ,"<regId=thiaz_ene_A(128)>"
"n2(-[#6]:1:[!#1]:[#6]:[#6]:[#6]:[#6]:1)c(cc(c2-[#6;X4])-[#1])-[#6;X4]"                                                                                                                                                                                                                                                                                                                                             ,"<regId=pyrrole_A(118)>"
"c:1:c:c(:c(:c:c:1)-[#8]-[#1])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                           ,"<regId=catechol_A(92)>"
"[#6]-1(=[#6])-[#6](-[#7]=[#6]-[#16]-1)=[#8]"                                                                                                                                                                                                                                                                                                                                                                       ,"<regId=ene_five_het_B(90)>"
"[#6]-1=[!#1]-[!#6&!#1]-[#6](-[#6]-1=[!#6&!#1;!R])=[#8]"                                                                                                                                                                                                                                                                                                                                                            ,"<regId=imine_one_fives(89)>"
"[#6]-1(-[#6](-[#6]=[#6]-[!#6&!#1]-1)=[#6])=[!#6&!#1]"                                                                                                                                                                                                                                                                                                                                                              ,"<regId=ene_five_het_C(85)>"
"[#6]-[#7]-1-[#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])(-[#1])-[#6]-1(-[#1])-[#1])-[#7]=[#6](-[#1])-[#6]:[!#1]"                                                                                                                                                                                                                                                                                        ,"<regId=hzone_pipzn(79)>"
"c:1-2:c(:c:c:c:c:1)-[#6](=[#8])-[#6;X4]-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                                                                               ,"<regId=keto_keto_beta_A(68)>"
"n1(-[#6])c(c(-[#1])c(c1-[#6]=[#7]-[#7])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                                ,"<regId=hzone_pyrrol(64)>"
"[#6]=!@[#6](-[!#1])-@[#6](=!@[!#6&!#1])-@[#6](=!@[#6])-[!#1]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=ene_one_ene_A(57)>"
"[#6](-[#6]#[#7])(-[#6]#[#7])-[#6](-[#7](-[#1])-[#1])=[#6]-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                               ,"<regId=cyano_ene_amine_A(56)>"
"c:1-2:c(:c:c:c:c:1)-[#6](=[#8])-[#6](=[#6])-[#6]-2=[#8]"                                                                                                                                                                                                                                                                                                                                                           ,"<regId=ene_five_one_A(55)>"
"[#6]-,:1(=,:[!#1]-,:[!#1]=,:[!#1]-,:[#7](-,:[#6]-,:1=[#16])-[#1])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                       ,"<regId=cyano_pyridone_A(54)>"
"c:1:c:c-2:c(:c:c:1)-[#6]-3-[#6](-[#6]-[#7]-2)-[#6]-[#6]=[#6]-3"                                                                                                                                                                                                                                                                                                                                                    ,"<regId=anil_alk_ene(51)>"
"c:1:c:2:c(:c:c:c:1):n:c:3:c(:c:2-[#7]):c:c:c:c:3"                                                                                                                                                                                                                                                                                                                                                                  ,"<regId=amino_acridine_A(46)>"
"[#6]-1(=[#6])-[#6](=[#8])-[#7]-[#7]-[#6]-1=[#8]"                                                                                                                                                                                                                                                                                                                                                                   ,"<regId=ene_five_het_D(46)>"
"[#7](-[#1])(-[#1])-c:1:c(:c(:c(:s:1)-[!#1])-[!#1])-[#6]=[#8]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=thiophene_amino_Aa(45)>"
"[#7]-[#6]=!@[#6]-2-[#6](=[#8])-c:1:c:c:c:c:c:1-[!#6&!#1]-2"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=ene_five_het_E(44)>"
"c:1(:c(:c(:c(:c(:c:1-[#8]-[#1])-[F,Cl,Br,I])-[#1])-[F,Cl,Br,I])-[#1])-[#16](=[#8])(=[#8])-[#7]"                                                                                                                                                                                                                                                                                                                    ,"<regId=sulfonamide_A(43)>"
"[#6]-[#6](=[#16])-[#6]"                                                                                                                                                                                                                                                                                                                                                                                            ,"<regId=thio_ketone(43)>"
"c:1:c:c(:c:c:c:1-[#8]-[#1])-[#7](-[#1])-[#16](=[#8])=[#8]"                                                                                                                                                                                                                                                                                                                                                         ,"<regId=sulfonamide_B(41)>"
"c:1(:c(:c(:c(:c(:c:1-[#1])-[#1])-[$([#8]),$([#7]),$([#6](-[#1])-[#1])])-[#1])-[#1])-[#7](-[#1])-[#1]"                                                                                                                                                                                                                                                                                                              ,"<regId=anil_no_alk(40)>"
"[c;!H0,$(c-[#6](-[#1])-[#1]),$(c-[#6]:[#6])]:1:c(:c(:c(:s:1)-[#7](-[#1])-[#6](=[#8])-[#6])-[#6](=[#8])-[#8])-[$([#6]:1:[#6]:[#6]:[#6]:[#6]:[#6]:1),$([#6]:1:[#16]:[#6]:[#6]:[#6]:1)]"                                                                                                                                                                                                                              ,"<regId=thiophene_amino_Ab(40)>"
"[#7+]:1(:[#6]:[#6]:[!#1]:c:2:c:1:c(:[c;!H0,$(c-[#7])]:c:c:2)-[#1])-[$([#6](-[#1])(-[#1])-[#1]),$([#8;X1]),$([#6](-[#1])(-[#1])-[#6](-[#1])=[#6](-[#1])-[#1]),$([#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#8]-[#1]),$([#6](-[#1])(-[#1])-[#6](=[#8])-[#6]),$([#6](-[#1])(-[#1])-[#6](=[#8])-[#7](-[#1])-[#6]:[#6]),$([#6](-[#1])(-[#1])-[#6](-[#1])(-[#1])-[#1])]"                                                     ,"<regId=het_pyridiniums_A(39)>"
"c:1:c:c:c:c(:c:1-[#7&!H0;!H1,!$([#7]-[#6]=[#8])])-[#6](-[#6]:[#6])=[#8]"                                                                                                                                                                                                                                                                                                                                           ,"<regId=anthranil_one_A(38)>"
"[#7](-[#1])-[#7]=[#6](-[#6]#[#7])-[#6]=[!#6&!#1;!R]"                                                                                                                                                                                                                                                                                                                                                               ,"<regId=cyano_imine_A(37)>"
"[#7](-c:1:c:c:c:c:c:1)-[#16](=[#8])(=[#8])-[#6]:2:[#6]:[#6]:[#6]:[#6]:3:[#7]:[$([#8]),$([#16])]:[#7]:[#6]:2:3"                                                                                                                                                                                                                                                                                                     ,"<regId=diazox_sulfon_A(36)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:1:c(:c(:c(:c(:c:1-[#1])-[#1])-[#6](-[#1])=[#7]-[#7]-[$([#6](=[#8])-[#6](-[#1])(-[#1])-[#16]-[#6]:[#7]),$([#6](=[#8])-[#6](-[#1])(-[#1])-[!#1]:[!#1]:[#7]),$([#6](=[#8])-[#6]:[#6]-[#8]-[#1]),$([#6]:[#7]),$([#6](-[#1])(-[#1])-[#6](-[#1])-[#8]-[#1])])-[#1])-[#1]"                                                                                                   ,"<regId=hzone_anil_di_alk(35)>"
"[#7]-1-[#6](=[#16])-[#16]-[#6;X4]-[#6]-1=[#8]"                                                                                                                                                                                                                                                                                                                                                                     ,"<regId=rhod_sat_A(33)>"
"[#7](-[#1])-[#7]=[#6]-[#6;!H0,$([#6]-[#6])]=[#6](-[#6])-!@[$([#7]),$([#8]-[#1])]"                                                                                                                                                                                                                                                                                                                                  ,"<regId=hzone_enamin(30)>"
"n2(-[#6]:1:[!#1]:[#6]:[#6]:[#6]:[#6]:1)c(cc(c2-[#6]:[#6])-[#1])-[#6;X4]"                                                                                                                                                                                                                                                                                                                                           ,"<regId=pyrrole_B(29)>"
"s1ccc(c1)-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                                               ,"<regId=thiophene_hydroxy(28)>"
"[#6]-,:1(=,:[#6](-,:[#6](=[#8])-,:[#7]-,:[#6](=,:[#7]-,:1)-,:[!#6&!#1])-[#6]#[#7])-[#6]"                                                                                                                                                                                                                                                                                                                           ,"<regId=cyano_pyridone_B(27)>"
"[#6]-1(-[#6](=[#8])-[#7]-[#6](=[#8])-[#7]-[#6]-1=[#8])=[#7]"                                                                                                                                                                                                                                                                                                                                                       ,"<regId=imine_one_sixes(27)>"
"[#6](-[#1])(-[#1])-[#7]([#6]:[#6])~[#6][#6]=,:[#6]-[#6]~[#6][#7]"                                                                                                                                                                                                                                                                                                                                                  ,"<regId=dyes5A(27)>"
"c:2:c:1:c:c:c:c-,:3:c:1:c(:c:c:2)-,:[#7]-,:[#6]=,:[#7]-,:3"                                                                                                                                                                                                                                                                                                                                                        ,"<regId=naphth_amino_A(25)>"
"c:2:c:1:c:c:c:c-3:c:1:c(:c:c:2)-[#7](-[#6;X4]-[#7]-3-[#1])-[#1]"                                                                                                                                                                                                                                                                                                                                                   ,"<regId=naphth_amino_B(25)>"
"[#6]-[#6](=[#8])-[#6](-[#1])=[#6](-[#7](-[#1])-[#6])-[#6](=[#8])-[#8]-[#6]"                                                                                                                                                                                                                                                                                                                                        ,"<regId=ene_one_ester(24)>"
"[#16]=[#6]-1-[#6]=,:[#6]-[!#6&!#1]-[#6]=,:[#6]-1"                                                                                                                                                                                                                                                                                                                                                                  ,"<regId=thio_dibenzo(23)>"
"[#6](-[#6]#[#7])(-[#6]#[#7])-[#6](-[$([#6]#[#7]),$([#6]=[#7])])-[#6]#[#7]"                                                                                                                                                                                                                                                                                                                                         ,"<regId=cyano_cyano_A(23)>"
"c:1:2:c(:c(:c(:c(:c:1:c(:c(:c(:c:2-[#1])-[#8]-[#1])-[#6](=[#8])-[#7](-[#1])-[#7]=[#6])-[#1])-[#1])-[#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                               ,"<regId=hzone_acyl_naphthol(22)>"
"[#8]=[#6]-c2c1nc(-[#6](-[#1])-[#1])cc(-[#8]-[#1])n1nc2"                                                                                                                                                                                                                                                                                                                                                            ,"<regId=het_65_A(21)>"
"n:1:c(:n(:c(:c:1-c:2:c:c:c:c:c:2)-c:3:c:c:c:c:c:3)-[#1])-[#6]:[!#1]"                                                                                                                                                                                                                                                                                                                                               ,"<regId=imidazole_A(19)>"
"[#6](-[#6]#[#7])(-[#6]#[#7])=[#6]-c:1:c:c:c:c:c:1"                                                                                                                                                                                                                                                                                                                                                                 ,"<regId=ene_cyano_A(19)>"
"c:1(:c:c:c:c:c:1-[#7](-[#1])-[#7]=[#6])-[#6](=[#8])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                     ,"<regId=anthranil_acid_A(19)>"
"[#7+]([#6]:[#6])=,:[#6]-[#6](-[#1])=[#6]-[#7](-[#6;X4])-[#6]"                                                                                                                                                                                                                                                                                                                                                      ,"<regId=dyes3A(19)>"
"[#7](-[#1])(-[#1])-[#6]-1=[#6](-[#6]#[#7])-[#6](-[#1])(-[#6]:[#6])-[#6](=[#6](-[#7](-[#1])-[#1])-[#16]-1)-[#6]#[#7]"                                                                                                                                                                                                                                                                                               ,"<regId=dhp_bis_amino_CN(19)>"
"[#7]~[#6]:1:[#7]:[#7]:[#6](:[$([#7]),$([#6]-[#1]),$([#6]-[#7]-[#1])]:[$([#7]),$([#6]-[#7])]:1)-[$([#7]-[#1]),$([#8]-[#6](-[#1])-[#1])]"                                                                                                                                                                                                                                                                            ,"<regId=het_6_tetrazine(18)>"
"[#6]-[#6]=[#6](-[F,Cl,Br,I])-[#6](=[#8])-[#6]"                                                                                                                                                                                                                                                                                                                                                                     ,"<regId=ene_one_hal(17)>"
"[#6](-[#6]#[#7])(-[#6]#[#7])=[#7]-[#7](-[#1])-c:1:c:c:c:c:c:1"                                                                                                                                                                                                                                                                                                                                                     ,"<regId=cyano_imine_B(17)>"
"[#6]-,:1(=,:[#6](-!@[#6](=[#8])-[#7]-[#6](-[#1])-[#1])-,:[#16]-,:[#6](-,:[#7]-,:1-,:[$([#6](-[#1])(-[#1])-[#6](-[#1])=[#6](-[#1])-[#1]),$([#6]:[#6])])=[#16])-,:[$([#7]-[#6](=[#8])-[#6]:[#6]),$([#7](-[#1])-[#1])]"                                                                                                                                                                                               ,"<regId=thiaz_ene_B(17)>"
"[#16]-1-[#6](=[#8])-[#7]-[#6](=[#8])-[#6]-1=[#6](-[#1])-[$([#6]-[#35]),$([#6]:[#6](-[#1]):[#6](-[F,Cl,Br,I]):[#6]:[#6]-[F,Cl,Br,I]),$([#6]:[#6](-[#1]):[#6](-[#1]):[#6]-[#16]-[#6](-[#1])-[#1]),$([#6]:[#6]:[#6]:[#6]:[#6]:[#6]:[#6]:[#6]:[#6]:[#6]-[#8]-[#6](-[#1])-[#1]),$([#6]:1:[#6](-[#6](-[#1])-[#1]):[#7](-[#6](-[#1])-[#1]):[#6](-[#6](-[#1])-[#1]):[#6]:1)]"                                              ,"<regId=ene_rhod_B(16)>"
"[#8]-,:1-,:[#6](-,:[#16]-,:c:2:c-,:1:c:c:c(:c:2)-,:[$([#7]),$([#8])])=[$([#8]),$([#16])]"                                                                                                                                                                                                                                                                                                                          ,"<regId=thio_carbonate_A(15)>"
"[#7](-[#6](-[#1])-[#1])(-[#6](-[#1])-[#1])-c:1:c(:c(:c(:o:1)-[#6]=[#7]-[#7](-[#1])-[#6]=[!#6&!#1])-[#1])-[#1]"                                                                                                                                                                                                                                                                                                     ,"<regId=anil_di_alk_furan_A(15)>"
"c:1(:c:c:c:c:c:1)-[#6](-[#1])=!@[#6]-3-[#6](=[#8])-c:2:c:c:c:c:c:2-[#16]-3"                                                                                                                                                                                                                                                                                                                                        ,"<regId=ene_five_het_F(15)>"
"[#6]-1(-[#6](~[!#6&!#1]~[#6]-[!#6&!#1]-[#6]-1=[!#6&!#1])~[!#6&!#1])=[#6;!R]-[#1]"                                                                                                                                                                                                                                                                                                                                  ,"<regId=ene_six_het_A(483)>"
"c:1:c:c(:c(:c:c:1)-[#6]=[#7]-[#7])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                      ,"<regId=hzone_phenol_A(479)>"
"[#6](-[#1])(-[#1])-[#7](-[#6](-[#1])-[#1])-c:1:c:c(:c(:[c;!H0,$(c-[#6](-[#1])-[#1]),$(c-[#8]-[#6](-[#1])(-[#1])-[#6](-[#1])-[#1])](:c:1))-[#7])-[#1]"                                                                                                                                                                                                                                                              ,"<regId=anil_di_alk_A(478)>"
"[n;!H0,$(n-[#6;!H0;!H1])]:1(c(c(c:2:c:1:c:c:c:c:2-[#1])-[#6;X4]-[#1])-[$([#6](-[#1])-[#1]),$([#6]=,:[!#6&!#1]),$([#6](-[#1])-[#7]),$([#6](-[#1])(-[#6](-[#1])-[#1])-[#6](-[#1])(-[#1])-[#7](-[#1])-[#6](-[#1])-[#1])])"                                                                                                                                                                                            ,"<regId=indol_3yl_alk(461)>"
"[!#6&!#1]=[#6]-1-[#6]=,:[#6]-[#6](=[!#6&!#1])-[#6]=,:[#6]-1"                                                                                                                                                                                                                                                                                                                                                       ,"<regId=quinone_A(370)>"
"[#7;!R]=[#7]"                                                                                                                                                                                                                                                                                                                                                                                                      ,"<regId=azo_A(324)>"
"[#6]-[#6](=[!#6&!#1;!R])-[#6](=[!#6&!#1;!R])-[$([#6]),$([#16](=[#8])=[#8])]"                                                                                                                                                                                                                                                                                                                                       ,"<regId=imine_one_A(321)>"
"[#7]-[#6;X4]-c:1:c:c:c:c:c:1-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                            ,"<regId=mannich_A(296)>"
"c:1:c:c(:c:c:c:1-[#7](-[#6;X4])-[#6;X4])-[#6]=[#6]"                                                                                                                                                                                                                                                                                                                                                                ,"<regId=anil_di_alk_B(251)>"
"c:1:c:c(:c:c:c:1-[#8]-[#6;X4])-[#7;$([#7!H0]-[#6;X4]),$([#7](-[#6;X4])-[#6;X4])]"                                                                                                                                                                                                                                                                                                                                  ,"<regId=anil_di_alk_C(246)>"
"[#7]-1-[#6](=[#16])-[#16]-[#6](=[#6])-[#6]-1=[#8]"                                                                                                                                                                                                                                                                                                                                                                 ,"<regId=ene_rhod_A(235)>"
"c:1(:c:c:c(:c:c:1)-[#6]=[#7]-[#7])-[#8]-[#1]"                                                                                                                                                                                                                                                                                                                                                                      ,"<regId=hzone_phenol_B(215)>"
"[#6]-1(=[#6])-[#6]=[#7]-[!#6&!#1]-[#6]-1=[#8]"                                                                                                                                                                                                                                                                                                                                                                     ,"<regId=ene_five_het_A(201)>"
"c:1:c:c(:c:c:c:1-[#7](-[#6;X4])-[#6;X4])-[#6;X4]-[$([#8]-[#1]),$([#6]=[#6]-[#1]),$([#7]-[#6;X4])]"                                                                                                                                                                                                                                                                                                                 ,"<regId=anil_di_alk_D(198)>"
"[#8]=[#6]-2-[#6](=!@[#7]-[#7])-c:1:c:c:c:c:c:1-[#7]-2"                                                                                                                                                                                                                                                                                                                                                             ,"<regId=imine_one_isatin(189)>"
"[#6](-[#1])-[#7](-[#6](-[#1])-[#1])-c:1:c(:c(:c(:[c;!H0,$(c-[#6](-[#1])-[#1])](:c:1-[#1]))-[#6&!H0;!H1,$([#6]-[#6;!H0])])-[#1])-[#1]"                                                                                                                                                                                                                                                                              ,"<regId=anil_di_alk_E(186)>"