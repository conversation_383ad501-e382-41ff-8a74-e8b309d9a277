data/*.csv filter=lfs diff=lfs merge=lfs -text
data/samples/*generated.csv filter=lfs diff=lfs merge=lfs -text
*.npz filter=lfs diff=lfs merge=lfs -text
data/11_p0.smi.gz filter=lfs diff=lfs merge=lfs -text
moses/dataset/data/test_scaffolds_stats.npz filter=lfs diff=lfs merge=lfs -text
moses/dataset/data/test_stats.npz filter=lfs diff=lfs merge=lfs -text
moses/dataset/data/test.csv.gz filter=lfs diff=lfs merge=lfs -text
moses/dataset/data/test_scaffolds.csv.gz filter=lfs diff=lfs merge=lfs -text
moses/dataset/data/train.csv.gz filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/combinatorial_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/metrics_combinatorial_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/hmm_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/metrics_hmm_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/latent_gan_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/aae_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/metrics_aae_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/char_rnn_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/metrics_ngram_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/metrics_vae_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/vae_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/ngram_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/aae_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/metrics_aae_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/metrics_ngram_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/latent_gan_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/metrics_vae_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/metrics_latent_gan_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/ngram_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/vae_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/vae_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/char_rnn_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/combinatorial_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/hmm_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/metrics_ngram_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/ngram_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/metrics_char_rnn_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/hmm_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/metrics_latent_gan_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/combinatorial_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/metrics_char_rnn_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/combinatorial_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/metrics_combinatorial_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/hmm_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/latent_gan_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/aae_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/metrics_aae_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/char_rnn_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/latent_gan_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/latent_gan/metrics_latent_gan_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/ngram/ngram_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/combinatorial/metrics_combinatorial_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/metrics_hmm_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/hmm/metrics_hmm_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/metrics_vae_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/vae/vae_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/aae/aae_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/char_rnn_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/char_rnn/metrics_char_rnn_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/jtn_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/jtn_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/jtn_all.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/metrics_jtn_1.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/metrics_jtn_2.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/metrics_jtn_3.csv filter=lfs diff=lfs merge=lfs -text
data/samples/jtn/jtn_1.csv filter=lfs diff=lfs merge=lfs -text
