#!/usr/bin/env python3
"""
分子扩散模型研究工具集
实现研究指南中定义的各种检查和验证功能
"""

import os
import sys
import json
import hashlib
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import uuid

import torch
import numpy as np
import pandas as pd
from rdkit import Chem
from rdkit.Chem import Descriptors, Crippen, Lipinski
import yaml


class ResearchToolkit:
    """分子扩散模型研究工具包"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self.load_config(config_path) if config_path else {}
        self.experiment_id = str(uuid.uuid4())
        
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)


class ExperimentTracker:
    """实验追踪器"""
    
    def __init__(self, experiment_name: str, output_dir: str = "experiments"):
        self.experiment_name = experiment_name
        self.output_dir = output_dir
        self.experiment_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # 创建实验目录
        self.experiment_dir = os.path.join(output_dir, f"{experiment_name}_{self.experiment_id[:8]}")
        os.makedirs(self.experiment_dir, exist_ok=True)
        
        # 初始化实验元数据
        self.metadata = self.initialize_metadata()
        
    def initialize_metadata(self) -> Dict:
        """初始化实验元数据"""
        return {
            "experiment_id": self.experiment_id,
            "experiment_name": self.experiment_name,
            "start_time": self.start_time.isoformat(),
            "git_commit": self.get_git_commit_hash(),
            "python_version": sys.version,
            "torch_version": torch.__version__,
            "cuda_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            "working_directory": os.getcwd(),
            "command_line": " ".join(sys.argv)
        }
    
    def get_git_commit_hash(self) -> str:
        """获取当前Git提交哈希"""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "HEAD"], 
                capture_output=True, text=True, check=True
            )
            return result.stdout.strip()
        except (subprocess.CalledProcessError, FileNotFoundError):
            return "unknown"
    
    def log_config(self, config: Dict):
        """记录实验配置"""
        self.metadata["config"] = config
        self.metadata["config_hash"] = hashlib.md5(
            json.dumps(config, sort_keys=True).encode()
        ).hexdigest()
        
    def log_metrics(self, metrics: Dict, step: Optional[int] = None):
        """记录实验指标"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            "timestamp": timestamp,
            "step": step,
            "metrics": metrics
        }
        
        # 保存到文件
        metrics_file = os.path.join(self.experiment_dir, "metrics.jsonl")
        with open(metrics_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
    
    def save_model(self, model: torch.nn.Module, name: str = "model.pt"):
        """保存模型"""
        model_path = os.path.join(self.experiment_dir, name)
        torch.save(model.state_dict(), model_path)
        self.metadata["saved_models"] = self.metadata.get("saved_models", [])
        self.metadata["saved_models"].append({
            "name": name,
            "path": model_path,
            "timestamp": datetime.now().isoformat()
        })
    
    def finalize_experiment(self):
        """完成实验，保存所有元数据"""
        self.metadata["end_time"] = datetime.now().isoformat()
        self.metadata["duration_seconds"] = (
            datetime.now() - self.start_time
        ).total_seconds()
        
        # 保存元数据
        metadata_file = os.path.join(self.experiment_dir, "metadata.json")
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)


class ChemicalValidator:
    """化学有效性验证器"""
    
    def __init__(self):
        self.validation_rules = {
            "valence_check": True,
            "aromaticity_check": True,
            "charge_balance": True,
            "ring_strain": True,
            "drug_likeness": True
        }
    
    def validate_molecule(self, mol: Chem.Mol) -> Dict[str, Any]:
        """验证单个分子的化学有效性"""
        if mol is None:
            return {"valid": False, "errors": ["Invalid molecule object"]}
        
        errors = []
        warnings = []
        
        # 基本有效性检查
        try:
            Chem.SanitizeMol(mol)
        except Exception as e:
            errors.append(f"Sanitization failed: {str(e)}")
            return {"valid": False, "errors": errors, "warnings": warnings}
        
        # 价键检查
        if self.validation_rules["valence_check"]:
            valence_errors = self.check_valence(mol)
            errors.extend(valence_errors)
        
        # 芳香性检查
        if self.validation_rules["aromaticity_check"]:
            aromaticity_warnings = self.check_aromaticity(mol)
            warnings.extend(aromaticity_warnings)
        
        # 电荷平衡检查
        if self.validation_rules["charge_balance"]:
            charge_warnings = self.check_charge_balance(mol)
            warnings.extend(charge_warnings)
        
        # 环张力检查
        if self.validation_rules["ring_strain"]:
            ring_warnings = self.check_ring_strain(mol)
            warnings.extend(ring_warnings)
        
        # 药物相似性检查
        if self.validation_rules["drug_likeness"]:
            drug_like_warnings = self.check_drug_likeness(mol)
            warnings.extend(drug_like_warnings)
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "molecular_properties": self.calculate_properties(mol)
        }
    
    def check_valence(self, mol: Chem.Mol) -> List[str]:
        """检查价键规则"""
        errors = []
        for atom in mol.GetAtoms():
            try:
                valence = atom.GetTotalValence()
                expected_valence = Chem.GetPeriodicTable().GetValenceList(atom.GetAtomicNum())
                if valence not in expected_valence:
                    errors.append(f"Atom {atom.GetIdx()} has invalid valence: {valence}")
            except Exception as e:
                errors.append(f"Valence check failed for atom {atom.GetIdx()}: {str(e)}")
        return errors
    
    def check_aromaticity(self, mol: Chem.Mol) -> List[str]:
        """检查芳香性"""
        warnings = []
        aromatic_atoms = [atom for atom in mol.GetAtoms() if atom.GetIsAromatic()]
        if aromatic_atoms:
            # 检查芳香环的完整性
            aromatic_rings = [ring for ring in mol.GetRingInfo().AtomRings() 
                            if all(mol.GetAtomWithIdx(idx).GetIsAromatic() for idx in ring)]
            if len(aromatic_rings) == 0 and len(aromatic_atoms) > 0:
                warnings.append("Aromatic atoms found but no complete aromatic rings")
        return warnings
    
    def check_charge_balance(self, mol: Chem.Mol) -> List[str]:
        """检查电荷平衡"""
        warnings = []
        total_charge = sum(atom.GetFormalCharge() for atom in mol.GetAtoms())
        if abs(total_charge) > 2:
            warnings.append(f"High total formal charge: {total_charge}")
        return warnings
    
    def check_ring_strain(self, mol: Chem.Mol) -> List[str]:
        """检查环张力"""
        warnings = []
        ring_info = mol.GetRingInfo()
        for ring in ring_info.AtomRings():
            ring_size = len(ring)
            if ring_size < 5:
                warnings.append(f"Small ring detected (size {ring_size}), potential strain")
            elif ring_size > 8:
                warnings.append(f"Large ring detected (size {ring_size}), check flexibility")
        return warnings
    
    def check_drug_likeness(self, mol: Chem.Mol) -> List[str]:
        """检查药物相似性（Lipinski规则）"""
        warnings = []
        
        # 分子量
        mw = Descriptors.MolWt(mol)
        if mw > 500:
            warnings.append(f"Molecular weight ({mw:.1f}) exceeds Lipinski limit (500)")
        
        # LogP
        logp = Crippen.MolLogP(mol)
        if logp > 5:
            warnings.append(f"LogP ({logp:.2f}) exceeds Lipinski limit (5)")
        
        # 氢键供体
        hbd = Lipinski.NumHDonors(mol)
        if hbd > 5:
            warnings.append(f"H-bond donors ({hbd}) exceed Lipinski limit (5)")
        
        # 氢键受体
        hba = Lipinski.NumHAcceptors(mol)
        if hba > 10:
            warnings.append(f"H-bond acceptors ({hba}) exceed Lipinski limit (10)")
        
        return warnings
    
    def calculate_properties(self, mol: Chem.Mol) -> Dict[str, float]:
        """计算分子性质"""
        return {
            "molecular_weight": Descriptors.MolWt(mol),
            "logp": Crippen.MolLogP(mol),
            "num_h_donors": Lipinski.NumHDonors(mol),
            "num_h_acceptors": Lipinski.NumHAcceptors(mol),
            "num_rotatable_bonds": Descriptors.NumRotatableBonds(mol),
            "num_aromatic_rings": Descriptors.NumAromaticRings(mol),
            "num_heavy_atoms": mol.GetNumHeavyAtoms(),
            "tpsa": Descriptors.TPSA(mol)
        }
    
    def validate_molecule_list(self, molecules: List[Chem.Mol]) -> Dict[str, Any]:
        """批量验证分子列表"""
        results = []
        valid_count = 0
        
        for i, mol in enumerate(molecules):
            result = self.validate_molecule(mol)
            result["molecule_index"] = i
            results.append(result)
            if result["valid"]:
                valid_count += 1
        
        return {
            "total_molecules": len(molecules),
            "valid_molecules": valid_count,
            "validity_rate": valid_count / len(molecules) if molecules else 0,
            "detailed_results": results
        }


class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.chemistry_keywords = [
            "mol", "smiles", "bond", "atom", "valence", "aromatic",
            "charge", "ring", "molecular", "chemical", "rdkit"
        ]
    
    def check_chemistry_code(self, file_path: str) -> Dict[str, Any]:
        """检查化学相关代码的质量"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        suggestions = []
        
        # 检查是否有适当的化学库导入
        if any(keyword in content.lower() for keyword in self.chemistry_keywords):
            if "from rdkit" not in content and "import rdkit" not in content:
                issues.append("使用化学相关功能但未导入RDKit")
        
        # 检查是否有分子有效性验证
        if "mol" in content.lower() and "sanitize" not in content.lower():
            suggestions.append("建议添加分子有效性验证（如Chem.SanitizeMol）")
        
        # 检查是否有错误处理
        if "Chem.MolFromSmiles" in content and "try:" not in content:
            suggestions.append("建议为SMILES解析添加错误处理")
        
        return {
            "file_path": file_path,
            "issues": issues,
            "suggestions": suggestions,
            "chemistry_related": any(keyword in content.lower() for keyword in self.chemistry_keywords)
        }


def setup_reproducible_environment(seed: int = 42):
    """设置可重现的实验环境"""
    import random
    
    # 设置随机种子
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    print(f"已设置随机种子: {seed}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")


if __name__ == "__main__":
    # 示例用法
    print("分子扩散模型研究工具集")
    print("=" * 50)
    
    # 设置可重现环境
    setup_reproducible_environment()
    
    # 创建实验追踪器
    tracker = ExperimentTracker("test_experiment")
    print(f"实验ID: {tracker.experiment_id}")
    print(f"实验目录: {tracker.experiment_dir}")
    
    # 化学验证示例
    validator = ChemicalValidator()
    
    # 测试一些分子
    test_smiles = ["CCO", "c1ccccc1", "CC(=O)OC1=CC=CC=C1C(=O)O", "invalid_smiles"]
    
    for smiles in test_smiles:
        mol = Chem.MolFromSmiles(smiles)
        result = validator.validate_molecule(mol)
        print(f"\nSMILES: {smiles}")
        print(f"有效: {result['valid']}")
        if result['errors']:
            print(f"错误: {result['errors']}")
        if result['warnings']:
            print(f"警告: {result['warnings']}")
