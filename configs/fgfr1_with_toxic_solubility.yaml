general:
    name: 'graph_dit_fgfr1_with_toxic_solubility'
    wandb: 'disabled'
    gpus: 1
    resume: null
    test_only: null
    sample_every_val: 100           # 每100轮验证采样一次（更频繁的多指标评估）
    samples_to_generate: 256        # 减少采样数量以平衡计算开销
    samples_to_save: 3
    chains_to_save: 1
    log_every_steps: 50
    number_chain_steps: 8
    final_model_samples_to_generate: 10000
    final_model_samples_to_save: 20
    final_model_chains_to_save: 1
    enable_progress_bar: True
    save_model: True
    # 多指标评估配置
    use_multi_metric_evaluation: True
    multi_metric_weights:
        chemical_validity: 0.25     # 化学有效性权重
        target_properties: 0.45     # 目标属性权重（多目标优化中更重要）
        molecular_diversity: 0.15   # 分子多样性权重
        synthetic_accessibility: 0.1 # 合成可达性权重
        nll_penalty: 0.05           # NLL惩罚项权重
dataset:
    name: 'fgfr1_with_toxic_solubility'
    path: 'data/raw/fgfr1_with_toxic_solubility.csv'
    split_ratio: [0.8, 0.1, 0.1]  # 训练集/验证集/测试集比例
    properties:
        - 'FGFR1_IC50'
        - 'toxicity'
        - 'solubility'
    
model:
    type: 'discrete'
    transition: 'marginal'                  
    model: 'graph_dit'
    diffusion_steps: 500
    diffusion_noise_schedule: 'cosine'
    guide_scale: 2
    hidden_size: 1536
    depth: 4
    num_heads: 12
    mlp_ratio: 4
    drop_condition: 0.01
    lambda_train: [1, 10]  # node and edge training weight 
    ensure_connected: True
    use_substructure: False       # 临时禁用以加速训练
    substructure_type: 'brics'
    substructure_guide_scale: 1.5
    # 化学约束相关配置
    use_chemical_constraints: True
    chemical_constraint_weights:
        valency: 0.15         # 原子价态约束权重（针对多目标优化适当增加）
        bond_type: 0.12       # 键类型合理性约束权重
        connectivity: 0.08    # 连通性约束权重
train:
    n_epochs: 10000
    batch_size: 128       # 进一步减小批次大小以节省GPU内存
    lr: 0.0002
    clip_grad: 1.0        # 添加梯度裁剪以稳定化学约束训练
    num_workers: 2        # 减少数据加载工作进程数以节省内存
    weight_decay: 1e-5    # 添加轻微的权重衰减
    seed: 0
    val_check_interval: null
    check_val_every_n_epoch: 1
    # 化学约束训练相关配置
    chemical_constraint_warmup_epochs: 100  # 化学约束权重预热轮数
    log_chemical_violations: True           # 记录化学违规统计
    # 性能优化配置
    compile_model: False                    # 是否编译模型（PyTorch 2.0+）
    mixed_precision: True                   # 启用混合精度训练
dataset:
    datadir: 'data/'
    task_name: 'fgfr1_with_toxic_solubility'
    guidance_target: 'ld50-sa-sc-label-solubility'  # 使用所有5个目标作为条件
    pin_memory: True              # 启用内存固定以加速GPU传输
    # 化学验证相关配置
    validate_molecules: True              # 启用分子化学有效性验证
    max_invalid_molecule_ratio: 0.1       # 最大无效分子比例阈值
    save_invalid_molecules: True          # 保存无效分子信息用于调试
    # 多目标优化相关配置
    target_weights:                       # 各目标的重要性权重
        fgfr1: 1.0                       # FGFR1活性
        ld50: 0.8                        # 毒性（LD50）
        sa: 0.6                          # 合成可达性
        sc: 0.6                          # 合成复杂度
        solubility: 0.7                  # 溶解性

# 采样和评估配置
sampling:
    # 化学约束采样配置
    use_chemical_constraints_sampling: True    # 在采样过程中应用化学约束
    max_sampling_attempts: 5                   # 最大采样尝试次数
    validate_generated_molecules: True         # 验证生成分子的化学有效性

    # 多目标采样配置
    multi_objective_sampling: True             # 启用多目标采样
    target_ranges:                            # 各目标的期望范围
        fgfr1: [6.0, 9.0]                    # FGFR1 pIC50范围
        ld50: [3.0, 6.0]                     # LD50范围（越高越安全）
        sa: [2.0, 4.0]                       # SA范围（越低越易合成）
        sc: [2.0, 4.0]                       # SC范围
        solubility: [-2.0, 2.0]              # 溶解性范围

# 评估和监控配置
evaluation:
    # 化学有效性评估
    chemical_validity_metrics: True           # 计算化学有效性指标
    molecular_diversity_metrics: True         # 计算分子多样性指标

    # 多目标评估
    multi_objective_metrics: True             # 计算多目标优化指标
    pareto_front_analysis: True               # 进行帕累托前沿分析

    # 评估频率
    evaluate_every_n_epochs: 100              # 每N轮进行一次详细评估
    save_evaluation_results: True             # 保存评估结果
