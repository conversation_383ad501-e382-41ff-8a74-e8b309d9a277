# 分子扩散模型研究配置模板
# 基于研究指南的标准化配置

# 实验基本信息
experiment:
  name: "molecular_diffusion_baseline"
  description: "基线分子扩散模型实验"
  tags: ["baseline", "qm9", "graph_transformer"]
  
# 数据集配置
dataset:
  name: "qm9"
  data_path: "data/processed/qm9"
  split_ratio: [0.8, 0.1, 0.1]  # train, val, test
  
  # 分子过滤器
  molecular_filters:
    max_atoms: 29
    min_atoms: 3
    max_molecular_weight: 500
    remove_salts: true
    remove_metals: true
    
  # 数据预处理
  preprocessing:
    standardize_smiles: true
    add_hydrogen: false
    compute_3d_coords: false
    
# 模型配置
model:
  architecture: "graph_transformer"
  
  # 图编码器
  graph_encoder:
    hidden_dim: 256
    num_layers: 6
    num_heads: 8
    dropout: 0.1
    
  # 扩散配置
  diffusion:
    num_timesteps: 1000
    noise_schedule: "cosine"  # linear, cosine, sigmoid
    beta_start: 0.0001
    beta_end: 0.02
    
  # 条件生成
  conditioning:
    use_molecular_properties: true
    property_dim: 64
    properties: ["molecular_weight", "logp", "tpsa"]
    
# 训练配置
training:
  batch_size: 32
  learning_rate: 1e-4
  num_epochs: 1000
  
  # 优化器
  optimizer:
    type: "adamw"
    weight_decay: 1e-5
    betas: [0.9, 0.999]
    
  # 学习率调度
  lr_scheduler:
    type: "cosine"
    warmup_epochs: 50
    min_lr: 1e-6
    
  # 正则化
  regularization:
    gradient_clip: 1.0
    dropout: 0.1
    
  # 早停
  early_stopping:
    patience: 50
    monitor: "val_chemical_validity"
    min_delta: 0.001
    
# 化学约束配置
chemistry:
  use_chemical_constraints: true
  
  # 有效性检查
  validity_checks:
    valence_check: true
    aromaticity_check: true
    charge_balance: true
    ring_strain_check: true
    
  # 药物相似性
  drug_likeness:
    use_lipinski_rules: true
    use_qed_score: true
    use_sa_score: true
    
  # 化学损失权重
  chemical_loss_weights:
    validity_weight: 1.0
    drug_likeness_weight: 0.5
    diversity_weight: 0.3
    
# 评估配置
evaluation:
  # 生成样本数量
  num_samples: 10000
  
  # 评估指标
  metrics:
    # 基本指标
    validity: true
    uniqueness: true
    novelty: true
    
    # 分布指标
    fcd_score: true  # Fréchet ChemNet Distance
    kl_divergence: true
    
    # 分子性质指标
    property_distribution: true
    scaffold_diversity: true
    functional_group_diversity: true
    
    # 药物相似性指标
    qed_score: true
    sa_score: true
    lipinski_compliance: true
    
  # 参考数据集（用于计算分布指标）
  reference_dataset: "data/processed/qm9/test.csv"
  
# 计算资源配置
compute:
  device: "cuda"  # cuda, cpu, auto
  num_workers: 4
  pin_memory: true
  
  # 内存优化
  memory_optimization:
    use_gradient_checkpointing: false
    use_mixed_precision: true
    dynamic_batching: true
    
  # 分布式训练
  distributed:
    use_ddp: false
    num_gpus: 1
    
# 日志和保存配置
logging:
  # 实验追踪
  use_wandb: true
  wandb_project: "molecular_diffusion"
  
  # 日志频率
  log_every_n_steps: 100
  validate_every_n_epochs: 10
  save_every_n_epochs: 50
  
  # 保存路径
  output_dir: "outputs"
  save_top_k: 3
  
# 可重现性配置
reproducibility:
  seed: 42
  deterministic: true
  benchmark: false
  
# AI工具使用配置
ai_assistance:
  # 代码生成验证
  validate_generated_code: true
  require_chemical_review: true
  
  # 自动化检查
  auto_code_quality_check: true
  auto_chemical_validation: true
  
  # 文档生成
  auto_generate_docs: false
  
# 质量控制配置
quality_control:
  # 代码审查
  require_peer_review: true
  chemistry_expert_review: true
  
  # 测试要求
  min_test_coverage: 0.8
  require_integration_tests: true
  
  # 性能监控
  monitor_training_stability: true
  detect_mode_collapse: true
  
  # 错误检测
  nan_detection: true
  gradient_monitoring: true
  memory_monitoring: true
