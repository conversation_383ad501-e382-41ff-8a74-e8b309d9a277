# 分子性质预测器

这个目录包含两个分子性质预测器：
1. `molecule_predictor.py` - 基于分子指纹的简单预测器
2. `graph_molecule_predictor.py` - 基于图神经网络的高级预测器（推荐使用）

## 模型权重

预训练的模型权重位于 `weight/7-10` 目录下，包括：
- `mouse_intraperitoneal_LD50_model.pt` - 模型权重文件
- `mouse_intraperitoneal_LD50_metadata.pkl` - 模型元数据文件

## 使用方法

### 1. 命令行使用

使用基于图神经网络的预测器进行预测：

```bash
# 预测单个分子
python test_graph_predictor.py --smiles="CCO" --explain

# 批量预测
python test_graph_predictor.py --file="molecules.csv" --output="results.csv"
```

参数说明：
- `--smiles`: 要预测的分子SMILES字符串
- `--explain`: 是否解释预测结果（包括分子基本性质）
- `--file`: 包含SMILES的CSV文件路径
- `--output`: 预测结果输出文件路径
- `--model_dir`: 模型权重目录，默认为 "predictor/weight/7-10"
- `--property`: 要预测的属性，默认为 "mouse_intraperitoneal_LD50"

### 2. 在Python代码中使用

```python
from predictor.graph_molecule_predictor import GraphMoleculePredictor

# 创建预测器实例
predictor = GraphMoleculePredictor(model_dir='predictor/weight/7-10')

# 预测单个分子
result = predictor.predict("CCO", "mouse_intraperitoneal_LD50")
print(f"预测值: {result['prediction']}")

# 解释预测结果
explanation = predictor.explain_prediction("CCO", "mouse_intraperitoneal_LD50")
print(f"预测值: {explanation['prediction']}")
print("分子性质:")
for prop, value in explanation['molecular_properties'].items():
    print(f"- {prop}: {value}")

# 批量预测
smiles_list = ["CCO", "c1ccccc1", "CC(=O)OC1=CC=CC=C1C(=O)O"]
results = predictor.predict_batch(smiles_list, "mouse_intraperitoneal_LD50")
for result in results:
    print(f"{result['smiles']}: {result['prediction']}")
```

## 模型说明

当前预训练模型可以预测：
- `mouse_intraperitoneal_LD50`: 小鼠腹腔LD50急性毒性（回归任务）

## 依赖库

- PyTorch
- RDKit
- NumPy
- Pandas
- scikit-learn 