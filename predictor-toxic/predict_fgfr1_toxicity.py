#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 使用训练好的毒性预测模型预测FGFR1抑制剂数据集中分子的毒性

import os
import torch
import numpy as np
import pandas as pd
import pickle
from tqdm import tqdm
import argparse
import sys
from datetime import datetime

# 导入模型训练器和相关类
from model_trainer import SimpleGNNPredictor, MoleculeDataset, collate_molgraphs

def collate_molgraphs_predict(data):
    """用于预测的分子图打包函数，处理没有标签的情况"""
    # 过滤掉创建失败的图
    valid_data = [d for d in data if d[0] is not None]
    if not valid_data:
        return None, None
    
    if len(valid_data[0]) == 3:  # 如果有标签
        return collate_molgraphs(valid_data)
    
    # 没有标签的情况
    graphs, smiles = map(list, zip(*valid_data))
    
    # 计算每个图的节点数和边数
    num_nodes = [g.num_nodes for g in graphs]
    cum_nodes = [0] + np.cumsum(num_nodes).tolist()
    
    # 合并所有图的节点特征
    all_node_features = torch.cat([g.node_features for g in graphs], dim=0)
    
    # 合并所有图的边索引和边特征，并调整边索引以反映批处理
    all_edge_indices = []
    all_edge_features = []
    
    for i, g in enumerate(graphs):
        if g.edge_index.shape[1] > 0:  # 如果有边
            # 调整边索引以适应批处理
            offset = cum_nodes[i]
            edge_index = g.edge_index.clone()
            edge_index[0, :] += offset
            edge_index[1, :] += offset
            all_edge_indices.append(edge_index)
            all_edge_features.append(g.edge_features)
    
    if all_edge_indices:
        all_edge_index = torch.cat(all_edge_indices, dim=1)
        all_edge_features = torch.cat(all_edge_features, dim=0)
    else:
        all_edge_index = torch.zeros((2, 0), dtype=torch.long)
        all_edge_features = torch.zeros((0, 6), dtype=torch.float32)
    
    # 创建节点到图的映射（每个节点属于哪个图）
    batch = torch.cat([torch.full((n,), i, dtype=torch.long) for i, n in enumerate(num_nodes)])
    
    return (all_node_features, all_edge_index, all_edge_features, batch, num_nodes), smiles

def parse_arguments():
    parser = argparse.ArgumentParser(description='使用训练好的毒性预测模型预测FGFR1抑制剂的毒性')
    parser.add_argument('--model-date', default="weight/2025-07-21",type=str, help='模型训练的日期目录名称，格式为YYYY-MM-DD')
    parser.add_argument('--input', type=str, default='FGFR1_IC50_with_props.csv', help='输入的FGFR1数据集文件路径')
    parser.add_argument('--output', type=str, default='fgfr1_toxicity_predictions.csv', help='输出预测结果的文件路径')
    parser.add_argument('--batch-size', type=int, default=64, help='批处理大小')
    return parser.parse_args()

class ToxicityPredictor:
    def __init__(self, model_dir='weight', model_date=None, property_name='ld50'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_dir = model_dir
        
        # 如果提供了日期，使用该日期目录
        if model_date:
            self.date_dir = os.path.join(model_dir, model_date)
            if not os.path.exists(self.date_dir):
                raise FileNotFoundError(f"未找到指定日期的模型目录: {self.date_dir}")
        else:
            # 如果没有提供日期，尝试找到最新的日期目录
            date_dirs = [d for d in os.listdir(model_dir) if os.path.isdir(os.path.join(model_dir, d))]
            if not date_dirs:
                raise FileNotFoundError(f"在{model_dir}中未找到任何日期目录")
            date_dirs.sort(reverse=True)  # 按日期降序排序
            self.date_dir = os.path.join(model_dir, date_dirs[0])
            print(f"未指定模型日期，使用最新的模型目录: {self.date_dir}")
        
        # 加载模型元数据
        metadata_path = os.path.join(self.date_dir, f'{property_name}_metadata.pkl')
        if not os.path.exists(metadata_path):
            raise FileNotFoundError(f"未找到模型元数据: {metadata_path}")
        
        with open(metadata_path, 'rb') as f:
            self.metadata = pickle.load(f)
        
        # 加载标准化参数
        if 'normalization_params' in self.metadata:
            self.normalization_params = self.metadata['normalization_params']
            print(f"已加载标准化参数: {self.normalization_params}")
        
        # 加载模型
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        # 根据元数据创建模型实例
        model_type = self.metadata.get('model_type', 'SimpleGNN')
        if model_type == 'PytorchTransformer':
            from model_trainer import MoleculeTransformerPredictor
            self.model = MoleculeTransformerPredictor(
                node_in_feats=self.metadata['node_in_feats'],
                edge_in_feats=self.metadata['edge_in_feats'],
                d_model=self.metadata.get('d_model', 128)
            )
        else:  # 默认使用SimpleGNN或HybridGNNTransformer
            self.model = SimpleGNNPredictor(
                node_in_feats=self.metadata['node_in_feats'],
                edge_in_feats=self.metadata['edge_in_feats'],
                hidden_dim=self.metadata.get('hidden_dim', 128),
                n_heads=self.metadata.get('n_heads', 8),
                num_layers=self.metadata.get('num_layers', 3)
            )
        
        # 尝试加载最佳模型，如果不存在则加载普通模型
        property_name = self.metadata['property_name']
        best_model_path = os.path.join(self.date_dir, f'{property_name}_model_best.pt')
        normal_model_path = os.path.join(self.date_dir, f'{property_name}_model.pt')
        
        if os.path.exists(best_model_path):
            model_path = best_model_path
            print(f"加载最佳模型: {best_model_path}")
        elif os.path.exists(normal_model_path):
            model_path = normal_model_path
            print(f"加载普通模型: {normal_model_path}")
        else:
            raise FileNotFoundError(f"未找到模型文件")
        
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.to(self.device)
        self.model.eval()
        print(f"模型已加载到{self.device}设备")
    
    def inverse_transform(self, normalized_values):
        """将标准化后的值转换回原始值"""
        if hasattr(self, 'normalization_params'):
            params = self.normalization_params
            # 反向Z-score标准化
            log_values = normalized_values * params['std'] + params['mean']
            # 反向对数转换
            if params['log_transform']:
                original_values = np.exp(log_values)
                return original_values
            return log_values
        return normalized_values
    
    def predict(self, smiles_list, batch_size=64):
        """预测分子的毒性"""
        dataset = MoleculeDataset(smiles_list)
        dataloader = torch.utils.data.DataLoader(
            dataset, 
            batch_size=batch_size, 
            shuffle=False, 
            collate_fn=collate_molgraphs_predict
        )
        
        all_preds = []
        valid_smiles = []
        invalid_indices = []
        
        with torch.no_grad():
            for i, batch_data_smiles in enumerate(tqdm(dataloader, desc="预测进度")):
                if isinstance(batch_data_smiles, tuple) and len(batch_data_smiles) == 2:
                    batch_data, smiles = batch_data_smiles
                elif isinstance(batch_data_smiles, tuple) and len(batch_data_smiles) == 3:
                    batch_data, _, smiles = batch_data_smiles
                else:
                    # 处理无效批次
                    batch_start_idx = i * batch_size
                    for j in range(min(batch_size, len(smiles_list) - batch_start_idx)):
                        invalid_indices.append(batch_start_idx + j)
                    continue
                
                if batch_data is None:
                    # 记录无效SMILES的索引
                    batch_start_idx = i * batch_size
                    for j, s in enumerate(smiles):
                        invalid_indices.append(batch_start_idx + j)
                    continue
                
                # 将数据移到设备
                batch_data = [t.to(self.device) if isinstance(t, torch.Tensor) else t for t in batch_data]
                
                # 预测
                output = self.model(batch_data)
                
                # 收集预测结果
                all_preds.extend(output.cpu().numpy().flatten())
                valid_smiles.extend(smiles)
        
        # 将标准化的预测值转换回原始值
        if hasattr(self, 'normalization_params'):
            print("将标准化的预测值转换回原始值...")
            all_preds = self.inverse_transform(np.array(all_preds))
        
        return all_preds, valid_smiles, invalid_indices

def main():
    args = parse_arguments()
    
    # 创建输出目录（如果不存在且输出路径包含目录）
    output_dir = os.path.dirname(args.output)
    if output_dir:  # 只有当输出路径包含目录时才创建
        os.makedirs(output_dir, exist_ok=True)
    
    # 加载FGFR1数据集
    input_path = args.input
    if not os.path.exists(input_path) and not os.path.isabs(input_path):
        # 尝试相对于项目根目录的路径
        root_input_path = os.path.join('..', input_path)
        if os.path.exists(root_input_path):
            input_path = root_input_path
    
    print(f"加载FGFR1数据集: {input_path}")
    df = pd.read_csv(input_path)
    smiles_list = df['smiles'].tolist()
    print(f"数据集包含 {len(smiles_list)} 个分子")
    
    # 初始化预测器
    predictor = ToxicityPredictor(model_dir='weight', model_date='2025-07-21', property_name='ld50')
    
    # 预测毒性
    print("开始预测毒性...")
    predictions, valid_smiles, invalid_indices = predictor.predict(smiles_list, batch_size=args.batch_size)
    
    # 创建结果DataFrame
    results = pd.DataFrame()
    results['smiles'] = valid_smiles
    results['predicted_ld50'] = predictions
    
    # 添加原始数据的其他列
    valid_df = df.drop(invalid_indices).reset_index(drop=True)
    for col in df.columns:
        if col != 'smiles' and col not in results.columns:
            results[col] = valid_df[col].values[:len(results)]
    
    # 只在终端输出中显示毒性分级信息，但不将其写入结果文件
    toxicity_levels = pd.cut(
        results['predicted_ld50'], 
        bins=[0, 5, 50, 300, 2000, 5000, float('inf')],
        labels=['剧毒', '高毒', '中等毒性', '低毒', '轻微毒性', '实际无毒']
    )
    
    # 保存结果
    results.to_csv(args.output, index=False)
    print(f"预测结果已保存到: {args.output}")
    
    # 输出统计信息
    print(f"\n毒性预测统计:")
    print(f"有效预测: {len(predictions)} 个分子")
    print(f"无效SMILES: {len(invalid_indices)} 个分子")
    
    # 创建一个临时DataFrame用于统计，不影响输出文件
    temp_df = pd.DataFrame()
    temp_df['toxicity_level'] = toxicity_levels
    print("\n毒性分布:")
    toxicity_counts = temp_df['toxicity_level'].value_counts()
    for level, count in toxicity_counts.items():
        print(f"- {level}: {count} 个分子 ({count/len(results)*100:.2f}%)")
    
    print(f"\nLD50预测值范围: [{results['predicted_ld50'].min():.2f}, {results['predicted_ld50'].max():.2f}]")
    print(f"LD50预测值均值: {results['predicted_ld50'].mean():.2f}")
    print(f"LD50预测值中位数: {results['predicted_ld50'].median():.2f}")

if __name__ == "__main__":
    main() 