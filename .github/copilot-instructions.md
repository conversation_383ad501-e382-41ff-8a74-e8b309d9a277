# Copilot Instructions for Graph-DiT-main-former

## 项目架构与核心模块
- 本项目聚焦于分子生成与分子性质预测，核心方法为图神经网络和扩散模型。
- 主要目录：
  - `graph_dit/`：扩散模型与分子生成主逻辑（如 `diffusion_model.py`, `main.py`）。
  - `predictor-toxic/`、`predictor-hob/`：分子毒性及其他性质预测，支持命令行和Python API。
  - `data/`：原始、处理后和分割的分子数据集，格式多为CSV/PT/JSON。
  - `mini-moses-master/`：分子生成基准评测工具。

## 关键开发流程
- 依赖安装：`pip install -r requirements.txt`，需 Python 3.7+，PyTorch，RDKit，pandas，numpy，scikit-learn。
- 训练/预测命令：
  - 单分子预测：`python predict_molecule.py predict --smiles "CCO" --property Mutagenicity`
  - 批量预测：`python predict_molecule.py predict --file molecules.csv --property hERG --output results.csv`
  - 训练新模型：`python predict_molecule.py train --data training_data.csv --property MyProperty --classification`
  - 列出模型：`python predict_molecule.py list`
- Python API 示例：
  ```python
  from molecule_property_predictor import MoleculePredictor
  predictor = MoleculePredictor()
  result = predictor.predict("CCO", "Mutagenicity")
  explanation = predictor.explain_prediction("CCO", "Mutagenicity")
  predictor.train("my_data.csv", "MyProperty", is_classification=True)
  ```

## 数据流与格式
- 训练数据：CSV，至少包含 `smiles` 和属性列，可选 `group` 列。
- 预测输出：JSON，分类任务含 `prediction` 和 `probability`，回归任务含 `prediction`。

## 项目约定与模式
- 属性预测支持解释性输出（`--explain`），结果包含分子基本性质。
- 预训练模型权重存放于 `weight/` 目录。
- 自定义模型需修改 `molecule_property_predictor.py`。
- 评测与基准采用 `mini-moses-master` 工具。

## 重要文件/目录参考
- `graph_dit/`, `predictor-toxic/`, `data/`, `mini-moses-master/`
- 相关 README.md 文件均有详细用法说明。

## 其他说明
- Clash 安装脚本与本项目无直接关联。
- 许可证为 MIT。

---
如有不清楚或遗漏的部分，请反馈以便进一步完善说明。
