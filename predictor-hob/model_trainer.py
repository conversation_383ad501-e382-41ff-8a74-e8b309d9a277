
# -*- coding: utf-8 -*-
# 训练模型，作用：预测是否有毒 心脏毒性 水溶性

import torch
import numpy as np
from rdkit import Chem
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import os
import pickle
import argparse
import sys
from sklearn.metrics import roc_auc_score, mean_squared_error, r2_score
from tqdm import tqdm, trange
from sklearn.model_selection import train_test_split
from datetime import datetime

# --- 分子图特征化 ---

def atom_featurizer(atom):
    """为原子生成特征向量"""
    return np.array(
        one_of_k_encoding_unk(atom.GetSymbol(),
                              ['C', 'N', 'O', 'S', 'F', 'Si', 'P', 'Cl', 'Br', 'Mg', 'Na', 'Ca', 'Fe', 'As', 'Al',
                               'I', 'B', 'V', 'K', 'Tl', 'Yb', 'Sb', 'Sn', 'Ag', 'Pd', 'Co', 'Se', 'Ti', 'Zn', 'H',
                               'Li', 'Ge', 'Cu', 'Au', '<PERSON>', 'Cd', 'In', 'Mn', 'Zr', 'Cr', 'Pt', 'Hg', 'Pb',
                               'Unknown']) +
        one_of_k_encoding(atom.GetDegree(), [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]) +
        one_of_k_encoding_unk(atom.GetImplicitValence(), [0, 1, 2, 3, 4, 5, 6]) +
        [atom.GetFormalCharge(), atom.GetNumRadicalElectrons()] +
        one_of_k_encoding_unk(atom.GetHybridization(),
                              [Chem.rdchem.HybridizationType.SP, Chem.rdchem.HybridizationType.SP2,
                               Chem.rdchem.HybridizationType.SP3, Chem.rdchem.HybridizationType.SP3D,
                               Chem.rdchem.HybridizationType.SP3D2]) +
        [atom.GetIsAromatic()]
    )

def one_of_k_encoding(x, allowable_set):
    if x not in allowable_set:
        raise Exception(f"输入 {x} 不在允许集合 {allowable_set} 中")
    return list(map(lambda s: x == s, allowable_set))

def one_of_k_encoding_unk(x, allowable_set):
    if x not in allowable_set:
        x = allowable_set[-1]
    return list(map(lambda s: x == s, allowable_set))

def bond_featurizer(bond):
    """为化学键生成特征向量"""
    bt = bond.GetBondType()
    return np.array([
        bt == Chem.rdchem.BondType.SINGLE,
        bt == Chem.rdchem.BondType.DOUBLE,
        bt == Chem.rdchem.BondType.TRIPLE,
        bt == Chem.rdchem.BondType.AROMATIC,
        bond.GetIsConjugated(),
        bond.IsInRing()
    ])

class MolecularGraph:
    """分子图表示，不依赖DGL"""
    def __init__(self, node_features, edge_index, edge_features):
        self.node_features = node_features  # [num_nodes, node_feature_dim]
        self.edge_index = edge_index        # [2, num_edges]
        self.edge_features = edge_features  # [num_edges, edge_feature_dim]
        self.num_nodes = node_features.shape[0]

class MolGraphFeaturizer:
    """从SMILES字符串生成分子图"""
    def __init__(self):
        pass

    def featurize(self, smiles):
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return None

            # 提取原子特征
            atom_feats = []
            for atom in mol.GetAtoms():
                atom_feats.append(atom_featurizer(atom))
            
            if len(atom_feats) == 0:
                return None
                
            node_features = torch.tensor(np.array(atom_feats), dtype=torch.float32)
            
            # 提取化学键特征和边索引
            edge_indices = []
            edge_feats = []
            for bond in mol.GetBonds():
                start, end = bond.GetBeginAtomIdx(), bond.GetEndAtomIdx()
                feats = bond_featurizer(bond)
                
                # 添加双向边
                edge_indices.append([start, end])
                edge_indices.append([end, start])
                edge_feats.append(feats)
                edge_feats.append(feats)
            
            if not edge_indices:  # 处理没有边的情况
                edge_index = torch.zeros((2, 0), dtype=torch.long)
                edge_features = torch.zeros((0, 6), dtype=torch.float32)
            else:
                edge_index = torch.tensor(edge_indices, dtype=torch.long).t()
                edge_features = torch.tensor(np.array(edge_feats), dtype=torch.float32)
            
            return MolecularGraph(node_features, edge_index, edge_features)
        except Exception as e:
            # print(f"为SMILES {smiles} 创建图时失败: {e}")
            return None

# --- 数据集和数据加载器 ---

class MoleculeDataset(Dataset):
    """分子数据集类 (纯PyTorch版本)"""
    def __init__(self, smiles_list, labels=None):
        self.smiles_list = smiles_list
        self.labels = labels
        self.featurizer = MolGraphFeaturizer()
        
    def __len__(self):
        return len(self.smiles_list)
    
    def __getitem__(self, idx):
        smiles = self.smiles_list[idx]
        graph = self.featurizer.featurize(smiles)
        
        # 如果图创建失败，返回None
        if graph is None:
            return None, None, None

        if self.labels is not None:
            return graph, self.labels[idx], smiles
        else:
            return graph, smiles

def collate_molgraphs(data):
    """将图、标签和SMILES打包成批次"""
    # 过滤掉创建失败的图
    valid_data = [d for d in data if d[0] is not None]
    if not valid_data:
        return None, None, None

    graphs, labels, smiles = map(list, zip(*valid_data))
    
    # 计算每个图的节点数和边数
    num_nodes = [g.num_nodes for g in graphs]
    cum_nodes = [0] + np.cumsum(num_nodes).tolist()
    
    # 合并所有图的节点特征
    all_node_features = torch.cat([g.node_features for g in graphs], dim=0)
    
    # 合并所有图的边索引和边特征，并调整边索引以反映批处理
    all_edge_indices = []
    all_edge_features = []
    
    for i, g in enumerate(graphs):
        if g.edge_index.shape[1] > 0:  # 如果有边
            # 调整边索引以适应批处理
            offset = cum_nodes[i]
            edge_index = g.edge_index.clone()
            edge_index[0, :] += offset
            edge_index[1, :] += offset
            all_edge_indices.append(edge_index)
            all_edge_features.append(g.edge_features)
    
    if all_edge_indices:
        all_edge_index = torch.cat(all_edge_indices, dim=1)
        all_edge_features = torch.cat(all_edge_features, dim=0)
    else:
        all_edge_index = torch.zeros((2, 0), dtype=torch.long)
        all_edge_features = torch.zeros((0, 6), dtype=torch.float32)
    
    # 创建节点到图的映射（每个节点属于哪个图）
    batch = torch.cat([torch.full((n,), i, dtype=torch.long) for i, n in enumerate(num_nodes)])
    
    # 转换标签
    batch_labels = torch.tensor(labels, dtype=torch.float32)
    
    return (all_node_features, all_edge_index, all_edge_features, batch, num_nodes), batch_labels, smiles

# --- Transformer 模型架构 ---

class GraphMultiHeadAttention(nn.Module):
    """图多头注意力层"""
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        assert self.head_dim * num_heads == d_model, "d_model必须能被num_heads整除"
        
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
    
    def forward(self, x, edge_index, edge_attr=None, batch=None, num_nodes=None):
        batch_size = len(num_nodes) if num_nodes else 1
        
        # 线性变换
        q = self.q_linear(x)  # [N, d_model]
        k = self.k_linear(x)  # [N, d_model]
        v = self.v_linear(x)  # [N, d_model]
        
        # 重塑为多头形式
        q = q.view(-1, self.num_heads, self.head_dim)  # [N, h, d_k]
        k = k.view(-1, self.num_heads, self.head_dim)  # [N, h, d_k]
        v = v.view(-1, self.num_heads, self.head_dim)  # [N, h, d_k]
        
        # 计算注意力分数 (使用边信息)
        # 为每条边计算注意力分数
        src, dst = edge_index
        q_dst = q[dst]  # [E, h, d_k]
        k_src = k[src]  # [E, h, d_k]
        
        # 计算注意力分数
        scores = torch.sum(q_dst * k_src, dim=-1) * self.scale  # [E, h]
        
        # 如果提供了边特征，将其融入注意力分数
        if edge_attr is not None:
            # 将边特征转换为每个头的权重
            edge_weights = torch.sigmoid(edge_attr @ torch.randn(edge_attr.shape[1], self.num_heads).to(edge_attr.device))
            scores = scores * edge_weights  # [E, h]
        
        # 对每个节点的所有传入边应用softmax
        # 我们需要按目标节点分组
        attention = torch.zeros_like(scores)
        
        # 对每个节点的传入边应用softmax
        for i in range(x.shape[0]):
            mask = (dst == i)
            if mask.sum() > 0:
                attention[mask] = F.softmax(scores[mask], dim=0)
        
        # 应用dropout
        attention = self.dropout(attention)
        
        # 计算加权和
        v_src = v[src]  # [E, h, d_k]
        
        # 初始化输出张量
        out = torch.zeros_like(x).view(-1, self.num_heads, self.head_dim)
        
        # 对每个节点，聚合其所有传入边的信息
        for i in range(x.shape[0]):
            mask = (dst == i)
            if mask.sum() > 0:
                # 计算加权和
                weighted_values = v_src[mask] * attention[mask].unsqueeze(-1)  # [num_edges_to_i, h, d_k]
                out[i] = weighted_values.sum(dim=0)  # [h, d_k]
        
        # 重塑回原始形状
        out = out.reshape(-1, self.d_model)  # [N, d_model]
        
        # 最终线性层
        out = self.out_linear(out)  # [N, d_model]
        
        return out

class GraphTransformerLayer(nn.Module):
    """图Transformer层"""
    def __init__(self, d_model, num_heads, dim_feedforward=2048, dropout=0.1):
        super().__init__()
        self.self_attn = GraphMultiHeadAttention(d_model, num_heads, dropout)
        
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
        self.activation = F.relu
    
    def forward(self, x, edge_index, edge_attr=None, batch=None, num_nodes=None):
        # 多头注意力
        attn_output = self.self_attn(x, edge_index, edge_attr, batch, num_nodes)
        x = x + self.dropout1(attn_output)
        x = self.norm1(x)
        
        # 前馈网络
        ff_output = self.linear2(self.dropout(self.activation(self.linear1(x))))
        x = x + self.dropout2(ff_output)
        x = self.norm2(x)
        
        return x

class MoleculeTransformerPredictor(nn.Module):
    """基于Transformer的分子性质预测模型 (纯PyTorch版本)"""
    def __init__(self, node_in_feats, edge_in_feats, num_layers=1, d_model=128, nhead=4,
                 dim_feedforward=512, ffn_hidden_feats=64, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        
        # 节点特征的初始嵌入层
        self.node_embedding = nn.Linear(node_in_feats, d_model)
        
        # Transformer层 - 减少为1层
        self.transformer_layers = nn.ModuleList([
            GraphTransformerLayer(d_model, nhead, dim_feedforward, dropout)
            for _ in range(num_layers)
        ])
        
        # 简化的预测头
        self.predict = nn.Sequential(
            nn.Linear(d_model, ffn_hidden_feats),
            nn.ReLU(),
            nn.Linear(ffn_hidden_feats, 1)
        )
    
    def forward(self, data):
        x, edge_index, edge_attr, batch, num_nodes = data
        
        # 初始嵌入
        x = self.node_embedding(x)
        
        # 通过Transformer层
        for layer in self.transformer_layers:
            x = layer(x, edge_index, edge_attr, batch, num_nodes)
        
        # 图级池化 (平均每个图中的节点)
        pooled = torch.zeros(len(num_nodes), self.d_model, device=x.device)
        start_idx = 0
        for i, nodes in enumerate(num_nodes):
            pooled[i] = x[start_idx:start_idx+nodes].mean(dim=0)
            start_idx += nodes
        
        # 预测
        out = self.predict(pooled)
        return out

# 新增简化的GNN模型，替代复杂的Transformer
class SimpleGNNPredictor(nn.Module):
    """简化的GNN模型，结合Transformer注意力机制用于分子性质预测"""
    def __init__(self, node_in_feats, edge_in_feats, hidden_dim=128, dropout=0.1, n_heads=4, num_layers=3):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.n_heads = n_heads
        self.head_dim = hidden_dim // n_heads
        self.num_layers = num_layers
        assert self.head_dim * n_heads == hidden_dim, "hidden_dim必须能被n_heads整除"
        
        # 节点特征的初始嵌入
        self.node_embedding = nn.Linear(node_in_feats, hidden_dim)
        
        # 边特征的嵌入
        self.edge_embedding = nn.Linear(edge_in_feats, hidden_dim)
        
        # 多层消息传递层
        self.message_layers = nn.ModuleList([
            nn.Linear(hidden_dim * 2 + hidden_dim, hidden_dim) for _ in range(num_layers)
        ])
        
        # 多层更新层
        self.update_layers = nn.ModuleList([
            nn.Linear(hidden_dim * 2, hidden_dim) for _ in range(num_layers)
        ])
        
        # 多层归一化
        self.norm_layers = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
        
        # 注意力机制
        self.q_linear = nn.Linear(hidden_dim, hidden_dim)
        self.k_linear = nn.Linear(hidden_dim, hidden_dim)
        self.v_linear = nn.Linear(hidden_dim, hidden_dim)
        self.attn_out = nn.Linear(hidden_dim, hidden_dim)
        
        # 层归一化
        self.norm_attn = nn.LayerNorm(hidden_dim)
        self.norm_ffn = nn.LayerNorm(hidden_dim)
        
        # FFN (Feed Forward Network)
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        self.dropout = nn.Dropout(dropout)
        
        # 预测头
        self.predict = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
    
    def message_passing(self, x, edge_index, edge_attr, message_layer, update_layer, norm_layer):
        src, dst = edge_index
        
        # 收集源节点和目标节点的特征
        src_feats = x[src]  # [E, H]
        dst_feats = x[dst]  # [E, H]
        
        # 构建消息
        messages = torch.cat([src_feats, dst_feats, edge_attr], dim=1)  # [E, 3*H]
        messages = F.relu(message_layer(messages))  # [E, H]
        
        # 聚合消息到目标节点
        aggregated_messages = torch.zeros_like(x)  # [N, H]
        for i in range(dst.shape[0]):
            aggregated_messages[dst[i]] += messages[i]
        
        # 更新节点特征
        updated_feats = torch.cat([x, aggregated_messages], dim=1)  # [N, 2*H]
        updated_feats = F.relu(update_layer(updated_feats))  # [N, H]
        
        # 残差连接和归一化
        x = x + self.dropout(updated_feats)
        x = norm_layer(x)
        
        return x
    
    def self_attention(self, x, mask=None):
        """多头自注意力机制"""
        batch_size = 1
        seq_len = x.size(0)
        
        # 始终将输入处理为3D张量 [B, N, H]
        if len(x.shape) == 2:  # [N, H]
            x = x.unsqueeze(0)  # [1, N, H]
        
        # 线性变换
        q = self.q_linear(x)  # [B, N, H]
        k = self.k_linear(x)  # [B, N, H]
        v = self.v_linear(x)  # [B, N, H]
        
        # 重塑为多头形式
        q = q.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)  # [B, h, N, d_k]
        k = k.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)  # [B, h, N, d_k]
        v = v.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)  # [B, h, N, d_k]
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)  # [B, h, N, N]
        
        # 应用mask (如果提供)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 应用softmax
        attn_weights = F.softmax(scores, dim=-1)  # [B, h, N, N]
        attn_weights = self.dropout(attn_weights)
        
        # 计算加权和
        attn_output = torch.matmul(attn_weights, v)  # [B, h, N, d_k]
        
        # 重塑回原始形状
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, self.hidden_dim)  # [B, N, H]
        attn_output = self.attn_out(attn_output)  # [B, N, H]
        
        # 始终返回2D张量 [N, H]
        return attn_output.squeeze(0)  # [N, H]
    
    def forward(self, data):
        x, edge_index, edge_attr, batch, num_nodes = data
        
        # 初始嵌入
        x = F.relu(self.node_embedding(x))
        edge_attr = F.relu(self.edge_embedding(edge_attr))
        
        # 多层消息传递 (GNN)
        for i in range(self.num_layers):
            x = self.message_passing(
                x, edge_index, edge_attr, 
                self.message_layers[i], 
                self.update_layers[i], 
                self.norm_layers[i]
            )
        
        # 自注意力 (Transformer)
        # 按照分子分组节点，为每个分子单独应用自注意力
        start_idx = 0
        attn_outputs = []
        
        for nodes in num_nodes:
            if nodes > 0:
                # 提取当前分子的节点特征
                mol_nodes = x[start_idx:start_idx+nodes]
                
                # 应用自注意力 - 确保返回的是2D张量 [nodes, hidden_dim]
                mol_attn = self.self_attention(mol_nodes)
                
                # 收集结果
                attn_outputs.append(mol_attn)
                
                # 更新起始索引
                start_idx += nodes
        
        # 合并所有分子的结果 - 现在所有张量都是2D的 [nodes, hidden_dim]
        x = torch.cat(attn_outputs, dim=0)
        
        # 残差连接和层归一化
        x = x + self.dropout(x)
        x = self.norm_attn(x)
        
        # FFN
        ffn_output = self.ffn(x)
        x = x + self.dropout(ffn_output)
        x = self.norm_ffn(x)
        
        # 图级池化 (平均每个图中的节点)
        pooled = torch.zeros(len(num_nodes), self.hidden_dim, device=x.device)
        start_idx = 0
        for i, nodes in enumerate(num_nodes):
            pooled[i] = x[start_idx:start_idx+nodes].mean(dim=0)
            start_idx += nodes
        
        # 预测
        out = self.predict(pooled)
        return out

# --- 模型训练器 ---

class ModelTrainer:
    """模型训练器类 (纯PyTorch版本)"""
    def __init__(self, model_dir='predictor-hob/weight'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_dir = model_dir
        os.makedirs(model_dir, exist_ok=True)

        # 创建以当前日期命名的子目录
        current_date = datetime.now().strftime("%Y-%m-%d")
        self.date_dir = os.path.join(model_dir, current_date)
        os.makedirs(self.date_dir, exist_ok=True)
        print(f"模型将保存在: {self.date_dir}")
    
    def preprocess_labels(self, labels, property_name=None):
        """对标签进行预处理，如对数转换等"""
        if property_name == "value":  # 水溶性数据预处理
            print("对水溶性数值进行预处理...")

            # 处理非数值条目
            processed_labels = []
            for label in labels:
                if pd.isna(label):
                    continue  # 跳过NaN值

                # 转换为字符串进行处理
                label_str = str(label)

                # 处理特殊格式的数值
                if '<' in label_str:
                    # 如 '<50[2]' 取50作为数值
                    try:
                        num = float(label_str.split('<')[1].split('[')[0])
                        processed_labels.append(num)
                    except:
                        continue
                elif '>' in label_str:
                    # 如 '>80[1]' 取80作为数值
                    try:
                        num = float(label_str.split('>')[1].split('[')[0])
                        processed_labels.append(num)
                    except:
                        continue
                elif '≈' in label_str:
                    # 如 '≈0[1]' 取0作为数值
                    try:
                        num = float(label_str.split('≈')[1].split('[')[0])
                        processed_labels.append(num)
                    except:
                        continue
                else:
                    # 尝试直接转换为数值
                    try:
                        num = float(label_str)
                        processed_labels.append(num)
                    except:
                        continue

            processed_labels = np.array(processed_labels)
            print(f"原始样本数: {len(labels)}, 处理后样本数: {len(processed_labels)}")
            print(f"水溶性数值范围: [{np.min(processed_labels):.2f}, {np.max(processed_labels):.2f}]")
            print(f"平均值: {np.mean(processed_labels):.2f}, 标准差: {np.std(processed_labels):.2f}")

            # 进行Z-score标准化
            mean = np.mean(processed_labels)
            std = np.std(processed_labels)
            normalized_labels = (processed_labels - mean) / std
            print(f"Z-score标准化后范围: [{np.min(normalized_labels):.4f}, {np.max(normalized_labels):.4f}]")

            # 保存标准化参数
            self.normalization_params = {
                'property': property_name,
                'log_transform': False,
                'mean': mean,
                'std': std
            }

            return normalized_labels

        elif property_name == "ld50":
            print("对LD50值进行对数转换，减小数值差异...")
            # 确保所有值为正数
            min_val = np.min(labels)
            if min_val <= 0:
                labels = labels + abs(min_val) + 1e-6

            # 对数转换
            transformed_labels = np.log(labels)
            print(f"转换前LD50范围: [{np.min(labels):.4f}, {np.max(labels):.4f}]")
            print(f"对数转换后LD50范围: [{np.min(transformed_labels):.4f}, {np.max(transformed_labels):.4f}]")

            # 进行Z-score标准化
            mean = np.mean(transformed_labels)
            std = np.std(transformed_labels)
            normalized_labels = (transformed_labels - mean) / std
            print(f"Z-score标准化后LD50范围: [{np.min(normalized_labels):.4f}, {np.max(normalized_labels):.4f}]")
            print(f"均值: {mean:.4f}, 标准差: {std:.4f}")

            # 保存标准化参数，用于后续预测时的转换
            self.normalization_params = {
                'property': property_name,
                'log_transform': True,
                'mean': mean,
                'std': std
            }

            return normalized_labels
        return labels
        
    def inverse_transform(self, normalized_values):
        """将标准化后的值转换回原始值"""
        if hasattr(self, 'normalization_params'):
            params = self.normalization_params
            # 反向Z-score标准化
            log_values = normalized_values * params['std'] + params['mean']
            # 反向对数转换
            if params['log_transform']:
                original_values = np.exp(log_values)
                return original_values
            return log_values
        return normalized_values
    
    def train(self, data_file=None, property_name=None, is_classification=False, epochs=50, batch_size=128, learning_rate=0.001, n_heads=4, num_layers=3, test_size=0.2, random_state=42, save_best_only=False):
        # 默认参数
        if data_file is None:
            data_file = 'predictor-hob/hob_data_set.csv'
        if property_name is None:
            property_name = 'value'
        data = pd.read_csv(data_file)
        if 'SMILES' in data.columns:
            smiles_list = data['SMILES'].tolist()
        elif 'Canonical SMILES' in data.columns:
            smiles_list = data['Canonical SMILES'].tolist()
        elif 'smiles' in data.columns:
            smiles_list = data['smiles'].tolist()
        elif 'smile' in data.columns:
            smiles_list = data['smile'].tolist()
        elif 'canonical_smiles' in data.columns:
            smiles_list = data['canonical_smiles'].tolist()
        else:
            raise ValueError("数据中没有找到SMILES或Canonical SMILES列")
        if property_name not in data.columns:
            raise ValueError(f"数据中没有找到属性列: {property_name}")
        labels = data[property_name].values

        # 对标签进行预处理（如对数转换等）
        processed_labels = self.preprocess_labels(labels, property_name)

        # 如果预处理后样本数量发生变化，需要同步处理SMILES
        if len(processed_labels) != len(smiles_list):
            print(f"预处理后样本数量变化: {len(smiles_list)} -> {len(processed_labels)}")
            # 对于水溶性数据，需要过滤掉无效的样本
            if property_name == "value":
                valid_indices = []

                for i, label in enumerate(labels):
                    if pd.isna(label):
                        continue

                    label_str = str(label)
                    try:
                        if '<' in label_str:
                            float(label_str.split('<')[1].split('[')[0])
                            valid_indices.append(i)
                        elif '>' in label_str:
                            float(label_str.split('>')[1].split('[')[0])
                            valid_indices.append(i)
                        elif '≈' in label_str:
                            float(label_str.split('≈')[1].split('[')[0])
                            valid_indices.append(i)
                        else:
                            float(label_str)
                            valid_indices.append(i)
                    except:
                        continue

                # 过滤SMILES列表
                smiles_list = [smiles_list[i] for i in valid_indices]
                print(f"过滤后的SMILES数量: {len(smiles_list)}")

        # 划分训练集和测试集
        smiles_train, smiles_test, labels_train, labels_test = train_test_split(smiles_list, processed_labels, test_size=test_size, random_state=random_state)
        print(f"训练集大小: {len(smiles_train)}，测试集大小: {len(smiles_test)}")
        train_dataset = MoleculeDataset(smiles_train, labels_train)
        test_dataset = MoleculeDataset(smiles_test, labels_test)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_molgraphs)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_molgraphs)
        # 获取特征维度
        for i in range(min(10, len(train_dataset))):
            sample_graph, _, _ = train_dataset[i]
            if sample_graph is not None:
                node_feats_dim = sample_graph.node_features.shape[1]
                edge_feats_dim = sample_graph.edge_features.shape[1] if sample_graph.edge_features.shape[0] > 0 else 6
                break
        else:
            raise ValueError("训练集中没有有效的分子图，请检查数据。")
        model = SimpleGNNPredictor(
            node_in_feats=node_feats_dim,
            edge_in_feats=edge_feats_dim,
            hidden_dim=128,
            dropout=0.1,
            n_heads=n_heads,
            num_layers=num_layers
        )
        model.to(self.device)
        print(f"混合GNN-Transformer模型已创建并移至{self.device}设备")
        # 根据任务类型选择损失函数
        if is_classification:
            criterion = nn.BCEWithLogitsLoss()
            print("使用二元分类损失函数 (BCEWithLogitsLoss)")
        else:
            criterion = nn.MSELoss()
            print("使用回归损失函数 (MSELoss)")
            
        optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        
        # 用于保存最佳模型的变量
        best_metric = float('inf') if not is_classification else 0.0
        best_epoch = -1
        
        print(f"开始训练 {property_name} {'分类' if is_classification else '回归'}模型，共 {epochs} 轮...")
        epoch_iter = trange(epochs, desc="训练进度")
        for epoch in epoch_iter:
            # 训练阶段
            model.train()
            total_loss = 0
            processed_batches = 0
            batch_iter = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}", leave=False)
            for batch_data, target, _ in batch_iter:
                if batch_data is None:
                    continue
                batch_data = [t.to(self.device) if isinstance(t, torch.Tensor) else t for t in batch_data]
                target = target.to(self.device).view(-1, 1)
                optimizer.zero_grad()
                output = model(batch_data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
                processed_batches += 1
                batch_iter.set_postfix(loss=f"{loss.item():.4f}")
            if processed_batches > 0:
                avg_loss = total_loss / processed_batches
                epoch_iter.set_postfix(avg_loss=f"{avg_loss:.4f}")
            
            # 测试阶段 - 每个epoch后评估模型
            model.eval()
            all_preds = []
            all_labels = []
            with torch.no_grad():
                for batch_data, target, _ in tqdm(test_loader, desc=f"Epoch {epoch+1} 测试评估", leave=False):
                    if batch_data is None:
                        continue
                    batch_data = [t.to(self.device) if isinstance(t, torch.Tensor) else t for t in batch_data]
                    target = target.to(self.device)
                    output = model(batch_data)
                    if is_classification:
                        probs = torch.sigmoid(output)
                        all_preds.extend(probs.cpu().numpy().flatten())
                    else:
                        all_preds.extend(output.cpu().numpy().flatten())
                    all_labels.extend(target.cpu().numpy().flatten())
            
            # 根据任务类型计算评估指标
            if is_classification:
                current_metric = roc_auc_score(all_labels, all_preds)
                is_better = current_metric > best_metric
                metric_name = "AUC"
            else:
                # 对于回归任务，先将预测值和标签转换回原始尺度
                if hasattr(self, 'normalization_params') and not is_classification:
                    print("将标准化的预测值转换回原始尺度...")
                    all_preds_original = self.inverse_transform(np.array(all_preds))
                    all_labels_original = self.inverse_transform(np.array(all_labels))
                    
                    # 计算原始尺度上的指标
                    mse_original = mean_squared_error(all_labels_original, all_preds_original)
                    r2_original = r2_score(all_labels_original, all_preds_original)
                    print(f"原始尺度上的MSE: {mse_original:.4f}, R²: {r2_original:.4f}")
                    
                    # 在标准化尺度上计算指标
                    current_metric = mean_squared_error(all_labels, all_preds)
                    r2 = r2_score(all_labels, all_preds)
                    print(f"标准化尺度上的MSE: {current_metric:.4f}, R²: {r2:.4f}")
                else:
                    current_metric = mean_squared_error(all_labels, all_preds)
                    r2 = r2_score(all_labels, all_preds)
                
                is_better = current_metric < best_metric
                metric_name = "MSE"
            
            r2 = r2_score(all_labels, all_preds) if not is_classification else 0
            print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.4f}, Test {metric_name}: {current_metric:.4f}" + 
                  (f", R²: {r2:.4f}" if not is_classification else ""))
            
            # 如果是当前最佳模型，保存它
            if is_better:
                best_metric = current_metric
                best_epoch = epoch + 1
                
                # 生成模型文件名
                model_path = os.path.join(self.date_dir, f'{property_name}_model_best.pt')
                
                # 如果启用了仅保存最佳模型选项，删除之前的最佳模型
                if save_best_only:
                    # 删除之前的最佳模型文件
                    prev_model_files = [f for f in os.listdir(self.date_dir) if f.startswith(f'{property_name}_model_') and f.endswith('.pt')]
                    for file in prev_model_files:
                        if file != f'{property_name}_model_best.pt':
                            try:
                                os.remove(os.path.join(self.date_dir, file))
                                print(f"已删除旧模型: {file}")
                            except Exception as e:
                                print(f"删除文件时出错: {e}")
                
                # 保存当前最佳模型
                torch.save(model.state_dict(), model_path)
                print(f"在第 {best_epoch} 轮发现新的最佳模型，{metric_name}: {best_metric:.4f}，已保存到 {model_path}")
                
                # 保存元数据
                metadata = {
                    'is_classification': is_classification,
                    'property_name': property_name,
                    'node_in_feats': node_feats_dim,
                    'edge_in_feats': edge_feats_dim,
                    'model_type': 'HybridGNNTransformer',
                    'hidden_dim': 128,
                    'n_heads': n_heads,
                    'num_layers': num_layers,
                    'training_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'train_samples': len(smiles_train),
                    'test_samples': len(smiles_test),
                    'data_file': data_file,
                    'best_epoch': best_epoch,
                    'best_metric': best_metric
                }
                
                # 添加标准化参数
                if hasattr(self, 'normalization_params'):
                    metadata['normalization_params'] = self.normalization_params
                    print("已保存标准化参数，用于后续预测")
                
                metadata_path = os.path.join(self.date_dir, f'{property_name}_metadata.pkl')
                with open(metadata_path, 'wb') as f:
                    pickle.dump(metadata, f)
                
                # 保存测试集评估结果
                eval_results = {
                    'metric': best_metric,
                    'test_labels': all_labels,
                    'test_predictions': all_preds,
                    'best_epoch': best_epoch,
                    'r2': r2 if not is_classification else None
                }
                eval_path = os.path.join(self.date_dir, f'{property_name}_test_results.pkl')
                with open(eval_path, 'wb') as f:
                    pickle.dump(eval_results, f)
        
        # 训练结束后，输出最佳模型信息
        metric_str = f"测试集{'AUC' if is_classification else 'MSE'}: {best_metric:.4f}"
        if not is_classification:
            # 计算最终R²
            final_r2 = r2_score(all_labels, all_preds)
            metric_str += f", R²: {final_r2:.4f}"
            
        print(f"\n训练完成！最佳模型来自第 {best_epoch} 轮，{metric_str}")
        print(f"最佳模型已保存到: {os.path.join(self.date_dir, f'{property_name}_model_best.pt')}")
        
        return model, {'best_epoch': best_epoch, 'best_metric': best_metric}
    
    def evaluate(self, model, data_file, property_name, is_classification=False, batch_size=128):
        data = pd.read_csv(data_file)
        if 'SMILES' in data.columns:
            smiles_list = data['SMILES'].tolist()
        elif 'Canonical SMILES' in data.columns:
            smiles_list = data['Canonical SMILES'].tolist()
        elif 'smiles' in data.columns:
            smiles_list = data['smiles'].tolist()
        elif 'smile' in data.columns:
            smiles_list = data['smile'].tolist()
        elif 'canonical_smiles' in data.columns:
            smiles_list = data['canonical_smiles'].tolist()
        else:
            raise ValueError("评估数据中没有找到SMILES或Canonical SMILES列")
            
        if property_name not in data.columns:
            raise ValueError(f"评估数据中没有找到属性列: {property_name}")
            
        labels = data[property_name].values
        
        # 如果有标准化参数，应用相同的转换
        if hasattr(self, 'normalization_params'):
            print("应用与训练时相同的标准化转换...")
            labels = self.preprocess_labels(labels, property_name)
        
        print(f"评估数据集大小: {len(smiles_list)} 分子")
        dataset = MoleculeDataset(smiles_list, labels)
        eval_loader = DataLoader(dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_molgraphs)
        
        model.eval()
        all_preds = []
        all_labels = []
        
        print("开始评估模型...")
        with torch.no_grad():
            # 添加进度条
            eval_iter = tqdm(eval_loader, desc="评估进度")
            for batch_data, target, _ in eval_iter:
                if batch_data is None:
                    continue
                
                # 将数据移到设备
                batch_data = [t.to(self.device) if isinstance(t, torch.Tensor) else t for t in batch_data]
                target = target.to(self.device)
                
                output = model(batch_data)
                
                if is_classification:
                    probs = torch.sigmoid(output)
                    all_preds.extend(probs.cpu().numpy().flatten())
                else:
                    all_preds.extend(output.cpu().numpy().flatten())
                
                all_labels.extend(target.cpu().numpy().flatten())
                
                # 更新进度条，显示已处理的样本数
                eval_iter.set_postfix(samples=f"{len(all_labels)}/{len(dataset)}")
        
        print("评估完成，计算指标...")
        if is_classification:
            auc = roc_auc_score(all_labels, all_preds)
            return {'auc': auc}
        else:
            # 在标准化尺度上计算指标
            mse = mean_squared_error(all_labels, all_preds)
            r2 = r2_score(all_labels, all_preds)
            
            # 如果有标准化参数，也计算原始尺度上的指标
            if hasattr(self, 'normalization_params'):
                print("将标准化的预测值转换回原始尺度...")
                all_preds_original = self.inverse_transform(np.array(all_preds))
                all_labels_original = self.inverse_transform(np.array(all_labels))
                
                mse_original = mean_squared_error(all_labels_original, all_preds_original)
                r2_original = r2_score(all_labels_original, all_preds_original)
                
                print(f"原始尺度上的MSE: {mse_original:.4f}, R²: {r2_original:.4f}")
                print(f"标准化尺度上的MSE: {mse:.4f}, R²: {r2:.4f}")
                
                return {'mse': mse, 'r2': r2, 'mse_original': mse_original, 'r2_original': r2_original}
            
            return {'mse': mse, 'r2': r2}

# --- 主函数和命令行接口 ---

def parse_arguments():
    parser = argparse.ArgumentParser(description='分子性质预测模型(纯PyTorch Graph Transformer)训练工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    train_parser = subparsers.add_parser('train', help='训练新的预测模型')
    train_parser.add_argument('--data', type=str, required=True)
    train_parser.add_argument('--property', type=str, required=True)
    train_parser.add_argument('--classification', action='store_true')
    train_parser.add_argument('--epochs', type=int, default=50)
    train_parser.add_argument('--batch-size', type=int, default=128)
    train_parser.add_argument('--learning-rate', type=float, default=0.001)
    
    eval_parser = subparsers.add_parser('evaluate', help='评估模型性能')
    eval_parser.add_argument('--data', type=str, required=True)
    eval_parser.add_argument('--property', type=str, required=True)
    eval_parser.add_argument('--batch-size', type=int, default=128)
    eval_parser.add_argument('--model-date', type=str, help='模型训练的日期目录名称，格式为YYYY-MM-DD')
    
    toxicity_parser = subparsers.add_parser('train_toxicity', help='快速训练毒性预测模型(回归预测LD50值)')
    toxicity_parser.add_argument('--epochs', type=int, default=30)
    toxicity_parser.add_argument('--batch-size', type=int, default=128)
    toxicity_parser.add_argument('--learning-rate', type=float, default=0.0001)
    toxicity_parser.add_argument('--n-heads', type=int, default=8, help='Transformer注意力头数')
    toxicity_parser.add_argument('--num-layers', type=int, default=3, help='GNN消息传递层数')

    solubility_parser = subparsers.add_parser('train_solubility', help='快速训练水溶性预测模型(回归预测水溶性值)')
    solubility_parser.add_argument('--epochs', type=int, default=30)
    solubility_parser.add_argument('--batch-size', type=int, default=128)
    solubility_parser.add_argument('--learning-rate', type=float, default=0.0001)
    solubility_parser.add_argument('--n-heads', type=int, default=8, help='Transformer注意力头数')
    solubility_parser.add_argument('--num-layers', type=int, default=3, help='GNN消息传递层数')
    
    return parser.parse_args()

def main():
    args = parse_arguments()
    trainer = ModelTrainer()
    
    if args.command == 'train':
        trainer.train(args.data, args.property, args.classification, args.epochs, args.batch_size, args.learning_rate)
    
    elif args.command == 'train_solubility':
        data_file = "predictor-hob/hob_data_set.csv"
        property_name = "value"
        print("开始训练水溶性预测模型...")
        print(f"使用hob_data_set.csv数据集，训练{args.epochs}轮...")
        print(f"使用高效的GNN消息传递（{args.num_layers}层）结合Transformer注意力机制（{args.n_heads}头注意力），兼顾效率与表达能力...")
        print("启用每轮测试评估和最佳模型保存功能，只保留性能最佳的模型...")
        print("注意: 训练水溶性回归模型，使用MSE作为评估指标")
        
        # 使用自定义参数训练，并启用仅保存最佳模型选项f
        result = trainer.train(
            data_file, 
            property_name, 
            is_classification=False, 
            epochs=args.epochs, 
            batch_size=args.batch_size, 
            learning_rate=args.learning_rate, 
            n_heads=args.n_heads, 
            num_layers=args.num_layers,
            save_best_only=True  # 启用仅保存最佳模型选项
        )
        
        print(f"训练完成！最佳模型来自第 {result[1]['best_epoch']} 轮，测试集MSE: {result[1]['best_metric']:.4f}")
        print(f"最佳模型已保存到: {trainer.date_dir}/{property_name}_model_best.pt")

    elif args.command == 'train_solubility':
        data_file = "predictor-hob/hob_data_set.csv"
        property_name = "value"
        print("开始训练水溶性预测模型...")
        print(f"使用hob_data_set.csv数据集，训练{args.epochs}轮...")
        print(f"使用高效的GNN消息传递（{args.num_layers}层）结合Transformer注意力机制（{args.n_heads}头注意力），兼顾效率与表达能力...")
        print("启用每轮测试评估和最佳模型保存功能，只保留性能最佳的模型...")
        print("注意: 训练水溶性回归模型，使用MSE作为评估指标")

        # 使用自定义参数训练，并启用仅保存最佳模型选项
        result = trainer.train(
            data_file,
            property_name,
            is_classification=False,
            epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate,
            n_heads=args.n_heads,
            num_layers=args.num_layers,
            save_best_only=True  # 启用仅保存最佳模型选项
        )

        print(f"训练完成！最佳模型来自第 {result[1]['best_epoch']} 轮，测试集MSE: {result[1]['best_metric']:.4f}")
        print(f"最佳模型已保存到: {trainer.date_dir}/{property_name}_model_best.pt")

    elif args.command == 'evaluate':
        # 如果提供了日期，使用该日期目录
        if hasattr(args, 'model_date') and args.model_date:
            date_dir = os.path.join(trainer.model_dir, args.model_date)
            if not os.path.exists(date_dir):
                raise FileNotFoundError(f"未找到指定日期的模型目录: {date_dir}")
        else:
            # 如果没有提供日期，尝试找到最新的日期目录
            date_dirs = [d for d in os.listdir(trainer.model_dir) if os.path.isdir(os.path.join(trainer.model_dir, d))]
            if not date_dirs:
                raise FileNotFoundError(f"在{trainer.model_dir}中未找到任何日期目录")
            date_dirs.sort(reverse=True)  # 按日期降序排序
            date_dir = os.path.join(trainer.model_dir, date_dirs[0])
            print(f"未指定模型日期，使用最新的模型目录: {date_dir}")
        
        metadata_path = os.path.join(date_dir, f'{args.property}_metadata.pkl')
        if not os.path.exists(metadata_path):
            raise FileNotFoundError(f"未找到模型元数据: {metadata_path}")
        with open(metadata_path, 'rb') as f:
            metadata = pickle.load(f)
            
        # 加载标准化参数
        if 'normalization_params' in metadata:
            trainer.normalization_params = metadata['normalization_params']
            print(f"已加载标准化参数: {trainer.normalization_params}")
            
        # 根据元数据加载正确的模型类型
        model_type = metadata.get('model_type', 'SimpleGNN')
        if model_type == 'PytorchTransformer':
            model = MoleculeTransformerPredictor(
                node_in_feats=metadata['node_in_feats'],
                edge_in_feats=metadata['edge_in_feats'],
                d_model=metadata.get('d_model', 128)
            )
        else:  # 默认使用SimpleGNN或HybridGNNTransformer
            model = SimpleGNNPredictor(
                node_in_feats=metadata['node_in_feats'],
                edge_in_feats=metadata['edge_in_feats'],
                hidden_dim=metadata.get('hidden_dim', 128),
                n_heads=metadata.get('n_heads', 8),
                num_layers=metadata.get('num_layers', 3)
            )
        
        # 尝试加载最佳模型，如果不存在则加载普通模型
        best_model_path = os.path.join(date_dir, f'{args.property}_model_best.pt')
        normal_model_path = os.path.join(date_dir, f'{args.property}_model.pt')
        
        if os.path.exists(best_model_path):
            model_path = best_model_path
            print(f"加载最佳模型: {best_model_path}")
        elif os.path.exists(normal_model_path):
            model_path = normal_model_path
            print(f"加载普通模型: {normal_model_path}")
        else:
            raise FileNotFoundError(f"未找到模型文件")
            
        model.load_state_dict(torch.load(model_path, map_location=trainer.device))
        model.to(trainer.device)
        
        metrics = trainer.evaluate(model, args.data, args.property, metadata['is_classification'], args.batch_size)
        print(f"{args.property} 模型评估结果:")
        for name, value in metrics.items():
            print(f"- {name}: {value:.4f}")
            
    else:
        print("请指定命令: train, evaluate, train_toxicity, train_solubility")

if __name__ == "__main__":
    sys.exit(main()) 