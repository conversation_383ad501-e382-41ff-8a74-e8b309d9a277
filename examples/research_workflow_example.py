#!/usr/bin/env python3
"""
分子扩散模型研究工作流程示例
展示如何使用研究工具包进行标准化的实验流程
"""

import os
import sys
import torch
import pandas as pd
from rdkit import Chem
import yaml

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.research_tools import (
    ExperimentTracker, 
    ChemicalValidator, 
    CodeQualityChecker,
    setup_reproducible_environment
)


class MolecularDiffusionExperiment:
    """分子扩散模型实验类 - 遵循研究指南的标准实现"""
    
    def __init__(self, config_path: str):
        """初始化实验"""
        # 1. 加载配置
        self.config = self.load_config(config_path)
        
        # 2. 设置可重现环境
        setup_reproducible_environment(self.config['reproducibility']['seed'])
        
        # 3. 初始化实验追踪
        self.tracker = ExperimentTracker(
            experiment_name=self.config['experiment']['name'],
            output_dir=self.config['logging']['output_dir']
        )
        
        # 4. 初始化化学验证器
        self.chemical_validator = ChemicalValidator()
        
        # 5. 记录实验配置
        self.tracker.log_config(self.config)
        
        print(f"实验初始化完成: {self.config['experiment']['name']}")
        print(f"实验ID: {self.tracker.experiment_id}")
        print(f"输出目录: {self.tracker.experiment_dir}")
    
    def load_config(self, config_path: str) -> dict:
        """加载实验配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证配置完整性
        required_sections = ['experiment', 'dataset', 'model', 'training', 'chemistry']
        for section in required_sections:
            if section not in config:
                raise ValueError(f"配置文件缺少必需的部分: {section}")
        
        return config
    
    def prepare_data(self):
        """数据准备阶段 - 遵循化学数据处理标准"""
        print("\n=== 数据准备阶段 ===")
        
        # 1. 加载原始数据
        data_path = self.config['dataset']['data_path']
        if not os.path.exists(data_path):
            print(f"警告: 数据路径不存在 {data_path}")
            # 创建示例数据用于演示
            self.create_example_data()
        
        # 2. 分子预处理
        molecules = self.preprocess_molecules()
        
        # 3. 化学有效性验证
        validation_results = self.validate_dataset(molecules)
        
        # 4. 记录数据统计
        self.tracker.log_metrics({
            "dataset_size": len(molecules),
            "validity_rate": validation_results['validity_rate'],
            "preprocessing_complete": True
        })
        
        print(f"数据准备完成: {len(molecules)} 个有效分子")
        return molecules
    
    def create_example_data(self):
        """创建示例数据用于演示"""
        example_smiles = [
            "CCO",  # 乙醇
            "c1ccccc1",  # 苯
            "CC(=O)OC1=CC=CC=C1C(=O)O",  # 阿司匹林
            "CN1C=NC2=C1C(=O)N(C(=O)N2C)C",  # 咖啡因
            "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O",  # 布洛芬
            "C1=CC=C(C=C1)C(=O)O",  # 苯甲酸
            "CCN(CC)CCNC(=O)C1=CC=C(C=C1)N",  # 普鲁卡因胺
            "C1=CC=C2C(=C1)C=CC=N2",  # 喹啉
        ]
        
        # 创建数据目录
        os.makedirs(self.config['dataset']['data_path'], exist_ok=True)
        
        # 保存示例数据
        df = pd.DataFrame({'smiles': example_smiles})
        df.to_csv(os.path.join(self.config['dataset']['data_path'], 'molecules.csv'), index=False)
        print("已创建示例数据集")
    
    def preprocess_molecules(self):
        """分子预处理 - 遵循化学数据处理标准"""
        # 读取数据
        data_file = os.path.join(self.config['dataset']['data_path'], 'molecules.csv')
        df = pd.read_csv(data_file)
        
        molecules = []
        filters = self.config['dataset']['molecular_filters']
        
        for smiles in df['smiles']:
            # 1. SMILES解析
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                continue
            
            # 2. 分子标准化
            if self.config['dataset']['preprocessing']['standardize_smiles']:
                try:
                    mol = Chem.MolFromSmiles(Chem.MolToSmiles(mol))
                except:
                    continue
            
            # 3. 应用分子过滤器
            if not self.passes_molecular_filters(mol, filters):
                continue
            
            molecules.append(mol)
        
        return molecules
    
    def passes_molecular_filters(self, mol: Chem.Mol, filters: dict) -> bool:
        """检查分子是否通过过滤器"""
        # 原子数量检查
        num_atoms = mol.GetNumHeavyAtoms()
        if num_atoms < filters['min_atoms'] or num_atoms > filters['max_atoms']:
            return False
        
        # 分子量检查
        from rdkit.Chem import Descriptors
        mw = Descriptors.MolWt(mol)
        if mw > filters['max_molecular_weight']:
            return False
        
        # 其他过滤器可以在这里添加
        
        return True
    
    def validate_dataset(self, molecules):
        """验证数据集的化学有效性"""
        print("正在验证数据集化学有效性...")
        
        validation_results = self.chemical_validator.validate_molecule_list(molecules)
        
        print(f"总分子数: {validation_results['total_molecules']}")
        print(f"有效分子数: {validation_results['valid_molecules']}")
        print(f"有效率: {validation_results['validity_rate']:.3f}")
        
        # 统计常见问题
        error_types = {}
        warning_types = {}
        
        for result in validation_results['detailed_results']:
            for error in result['errors']:
                error_types[error] = error_types.get(error, 0) + 1
            for warning in result['warnings']:
                warning_types[warning] = warning_types.get(warning, 0) + 1
        
        if error_types:
            print("\n常见错误:")
            for error, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                print(f"  {error}: {count} 次")
        
        if warning_types:
            print("\n常见警告:")
            for warning, count in sorted(warning_types.items(), key=lambda x: x[1], reverse=True):
                print(f"  {warning}: {count} 次")
        
        return validation_results
    
    def build_model(self):
        """构建模型 - 遵循模型架构标准"""
        print("\n=== 模型构建阶段 ===")
        
        # 这里应该实现实际的模型构建逻辑
        # 为了演示，我们创建一个简单的占位符模型
        
        class PlaceholderModel(torch.nn.Module):
            def __init__(self, config):
                super().__init__()
                self.config = config
                self.linear = torch.nn.Linear(100, 1)
            
            def forward(self, x):
                return self.linear(x)
        
        model = PlaceholderModel(self.config['model'])
        
        # 记录模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        self.tracker.log_metrics({
            "model_total_parameters": total_params,
            "model_trainable_parameters": trainable_params,
            "model_architecture": self.config['model']['architecture']
        })
        
        print(f"模型构建完成: {total_params:,} 个参数")
        return model
    
    def train_model(self, model, molecules):
        """训练模型 - 遵循训练标准和监控要求"""
        print("\n=== 模型训练阶段 ===")
        
        # 训练配置
        training_config = self.config['training']
        num_epochs = training_config['num_epochs']
        
        # 为演示目的，我们模拟训练过程
        for epoch in range(min(5, num_epochs)):  # 只运行5个epoch用于演示
            # 模拟训练指标
            train_loss = 1.0 - epoch * 0.1 + torch.randn(1).item() * 0.05
            val_loss = 1.1 - epoch * 0.08 + torch.randn(1).item() * 0.03
            
            # 模拟化学指标
            chemical_validity = 0.7 + epoch * 0.05 + torch.randn(1).item() * 0.02
            molecular_diversity = 0.8 + torch.randn(1).item() * 0.05
            
            # 记录训练指标
            metrics = {
                "epoch": epoch,
                "train_loss": train_loss,
                "val_loss": val_loss,
                "chemical_validity": chemical_validity,
                "molecular_diversity": molecular_diversity
            }
            
            self.tracker.log_metrics(metrics, step=epoch)
            
            print(f"Epoch {epoch}: train_loss={train_loss:.4f}, "
                  f"val_loss={val_loss:.4f}, validity={chemical_validity:.3f}")
            
            # 模拟保存最佳模型
            if epoch == 3:  # 假设第3个epoch是最佳的
                self.tracker.save_model(model, "best_model.pt")
        
        print("训练完成")
        return model
    
    def evaluate_model(self, model, molecules):
        """评估模型 - 遵循评估标准"""
        print("\n=== 模型评估阶段 ===")
        
        # 模拟生成分子
        print("生成分子样本...")
        num_samples = min(100, self.config['evaluation']['num_samples'])  # 演示用较小数量
        
        # 为演示，我们使用输入分子的子集作为"生成"的分子
        generated_molecules = molecules[:num_samples] if len(molecules) >= num_samples else molecules
        
        # 化学有效性评估
        validation_results = self.chemical_validator.validate_molecule_list(generated_molecules)
        
        # 计算其他评估指标
        evaluation_metrics = {
            "generated_samples": len(generated_molecules),
            "validity_rate": validation_results['validity_rate'],
            "uniqueness_rate": self.calculate_uniqueness(generated_molecules),
            "diversity_score": self.calculate_diversity(generated_molecules)
        }
        
        # 记录评估结果
        self.tracker.log_metrics(evaluation_metrics)
        
        print("评估指标:")
        for metric, value in evaluation_metrics.items():
            print(f"  {metric}: {value:.3f}" if isinstance(value, float) else f"  {metric}: {value}")
        
        return evaluation_metrics
    
    def calculate_uniqueness(self, molecules):
        """计算分子唯一性"""
        smiles_set = set()
        for mol in molecules:
            if mol is not None:
                smiles = Chem.MolToSmiles(mol)
                smiles_set.add(smiles)
        return len(smiles_set) / len(molecules) if molecules else 0
    
    def calculate_diversity(self, molecules):
        """计算分子多样性（简化版本）"""
        # 这里应该实现真正的多样性计算（如Tanimoto距离）
        # 为演示目的，返回一个模拟值
        return 0.85 + torch.randn(1).item() * 0.05
    
    def run_experiment(self):
        """运行完整的实验流程"""
        try:
            print(f"开始实验: {self.config['experiment']['name']}")
            print("=" * 60)
            
            # 1. 数据准备
            molecules = self.prepare_data()
            
            # 2. 模型构建
            model = self.build_model()
            
            # 3. 模型训练
            trained_model = self.train_model(model, molecules)
            
            # 4. 模型评估
            evaluation_results = self.evaluate_model(trained_model, molecules)
            
            # 5. 完成实验
            self.tracker.finalize_experiment()
            
            print("\n" + "=" * 60)
            print("实验完成!")
            print(f"实验结果保存在: {self.tracker.experiment_dir}")
            
            return evaluation_results
            
        except Exception as e:
            print(f"实验过程中发生错误: {str(e)}")
            self.tracker.finalize_experiment()
            raise


def main():
    """主函数 - 演示研究工作流程"""
    print("分子扩散模型研究工作流程演示")
    print("=" * 60)
    
    # 配置文件路径
    config_path = "configs/research_config_template.yaml"
    
    if not os.path.exists(config_path):
        print(f"错误: 配置文件不存在 {config_path}")
        print("请先运行配置文件生成脚本")
        return
    
    # 运行实验
    experiment = MolecularDiffusionExperiment(config_path)
    results = experiment.run_experiment()
    
    print("\n最终评估结果:")
    for metric, value in results.items():
        print(f"  {metric}: {value}")


if __name__ == "__main__":
    main()
