# These imports are tricky because they use c++, do not move them
import os, shutil
import warnings
import time
import datetime

import torch
import hydra
from omegaconf import DictConfig
from pytorch_lightning import Trainer
from pytorch_lightning.callbacks import TQDMProgressBar

import utils
from datasets import dataset
from diffusion_model import Graph_DiT
from metrics.molecular_metrics_train import TrainMolecularMetricsDiscrete
from metrics.molecular_metrics_sampling import SamplingMolecularMetrics

from analysis.visualization import MolecularVisualization

warnings.filterwarnings("ignore", category=UserWarning)
torch.set_float32_matmul_precision("medium")

# 自定义进度条回调
class CustomProgressBar(TQDMProgressBar):
    def __init__(self):
        super().__init__()
        self.enable = True
        self.start_time = None
        self.last_epoch = 0
        
    def on_train_epoch_start(self, trainer, pl_module):
        super().on_train_epoch_start(trainer, pl_module)
        if self.start_time is None:
            self.start_time = time.time()
        self.last_epoch = trainer.current_epoch
        
    def get_metrics(self, trainer, model):
        items = super().get_metrics(trainer, model)
        
        # 添加更多指标到进度条显示
        items["轮次"] = f"{trainer.current_epoch+1}/{trainer.max_epochs}"
        
        if hasattr(model, 'last_loss'):
            items["损失"] = f"{model.last_loss:.4f}"
            
        if hasattr(model, 'last_lr'):
            items["学习率"] = f"{model.last_lr:.6f}"
        
        # 添加批次处理速度指标
        if hasattr(model, 'batch_time') and model.batch_time > 0:
            items["批处理时间"] = f"{model.batch_time:.3f}s"
            
        if hasattr(model, 'batch_per_second') and model.batch_per_second > 0:
            items["批次/秒"] = f"{model.batch_per_second:.2f}"
        
        # 计算估计剩余时间
        if self.start_time is not None and trainer.current_epoch > 0:
            elapsed = time.time() - self.start_time
            time_per_epoch = elapsed / (trainer.current_epoch + 1)
            remaining_epochs = trainer.max_epochs - (trainer.current_epoch + 1)
            remaining_time = time_per_epoch * remaining_epochs
            
            # 将秒转换为可读格式
            remaining_time = str(datetime.timedelta(seconds=int(remaining_time)))
            items["预计剩余"] = remaining_time
            
        return items

def remove_folder(folder):
    for filename in os.listdir(folder):
        file_path = os.path.join(folder, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        except Exception as e:
            print("Failed to delete %s. Reason: %s" % (file_path, e))


def get_resume(cfg, model_kwargs):
    """Resumes a run. It loads previous config without allowing to update keys (used for testing)."""
    saved_cfg = cfg.copy()
    name = cfg.general.name + "_resume"
    resume = cfg.general.test_only
    batch_size = cfg.train.batch_size
    model = Graph_DiT.load_from_checkpoint(resume, **model_kwargs)
    cfg = model.cfg
    cfg.general.test_only = resume
    cfg.general.name = name
    cfg.train.batch_size = batch_size
    cfg = utils.update_config_with_new_keys(cfg, saved_cfg)
    return cfg, model

def get_resume_adaptive(cfg, model_kwargs):
    """Resumes a run. It loads previous config but allows to make some changes (used for resuming training)."""
    saved_cfg = cfg.copy()
    # Fetch path to this file to get base path
    current_path = os.path.dirname(os.path.realpath(__file__))
    root_dir = current_path.split("outputs")[0]

    resume_path = os.path.join(root_dir, cfg.general.resume)

    if cfg.model.type == "discrete":
        model = Graph_DiT.load_from_checkpoint(
            resume_path, **model_kwargs
        )
    else:
        raise NotImplementedError("Unknown model")

    new_cfg = model.cfg
    for category in cfg:
        for arg in cfg[category]:
            new_cfg[category][arg] = cfg[category][arg]

    new_cfg.general.resume = resume_path
    new_cfg.general.name = new_cfg.general.name + "_resume"

    new_cfg = utils.update_config_with_new_keys(new_cfg, saved_cfg)
    return new_cfg, model


@hydra.main(
    version_base="1.1", config_path="../configs", config_name="config"
)
def main(cfg: DictConfig):

    datamodule = dataset.DataModule(cfg)
    datamodule.prepare_data()
    dataset_infos = dataset.DataInfos(datamodule=datamodule, cfg=cfg)
    train_smiles, reference_smiles = datamodule.get_train_smiles()

    dataset_infos.compute_input_output_dims(datamodule=datamodule)
    train_metrics = TrainMolecularMetricsDiscrete(dataset_infos)

    sampling_metrics = SamplingMolecularMetrics(
        dataset_infos, train_smiles, reference_smiles
    )
    visualization_tools = MolecularVisualization(dataset_infos)

    model_kwargs = {
        "dataset_infos": dataset_infos,
        "train_metrics": train_metrics,
        "sampling_metrics": sampling_metrics,
        "visualization_tools": visualization_tools,
    }

    if cfg.general.test_only:
        # When testing, previous configuration is fully loaded
        cfg, _ = get_resume(cfg, model_kwargs)
        os.chdir(cfg.general.test_only.split("checkpoints")[0])
    elif cfg.general.resume is not None:
        # When resuming, we can override some parts of previous configuration
        cfg, _ = get_resume_adaptive(cfg, model_kwargs)
        os.chdir(cfg.general.resume.split("checkpoints")[0])

    model = Graph_DiT(cfg=cfg, **model_kwargs)
    
    # 创建进度条回调
    progress_bar = CustomProgressBar() if cfg.general.enable_progress_bar else None
    callbacks = [progress_bar] if progress_bar else []
    
    # 添加训练进度显示信息
    print(f"{'='*20} 训练配置 {'='*20}")
    print(f"模型名称: {cfg.general.name}")
    print(f"批次大小: {cfg.train.batch_size}")
    print(f"最大训练周期: {cfg.train.n_epochs}")
    print(f"学习率: {cfg.train.lr}")
    print(f"使用GPU数量: {cfg.general.gpus}")
    print(f"模型类型: {cfg.model.type}")
    print(f"扩散步数: {cfg.model.diffusion_steps}")
    print(f"{'='*50}")

    trainer = Trainer(
        gradient_clip_val=cfg.train.clip_grad,
        accelerator="gpu"
        if torch.cuda.is_available() and cfg.general.gpus > 0
        else "cpu",
        devices=cfg.general.gpus
        if torch.cuda.is_available() and cfg.general.gpus > 0
        else None,
        max_epochs=cfg.train.n_epochs,
        enable_checkpointing=False,
        check_val_every_n_epoch=cfg.train.check_val_every_n_epoch,
        val_check_interval=cfg.train.val_check_interval,
        strategy="ddp" if cfg.general.gpus > 1 else "auto",
        enable_progress_bar=cfg.general.enable_progress_bar,
        callbacks=callbacks,
        reload_dataloaders_every_n_epochs=0,
        logger=[],
    )

    if not cfg.general.test_only:
        print(f"{'='*20} 开始训练 {'='*20}")
        train_start_time = time.time()
        trainer.fit(model, datamodule=datamodule, ckpt_path=cfg.general.resume)
        train_end_time = time.time()
        train_duration = datetime.timedelta(seconds=int(train_end_time - train_start_time))
        print(f"{'='*20} 训练结束 {'='*20}")
        print(f"总训练时间: {train_duration}")
        
        if cfg.general.save_model:
            print(f"保存模型到 checkpoints/{cfg.general.name}/last.ckpt")
            trainer.save_checkpoint(f"checkpoints/{cfg.general.name}/last.ckpt")
            
        print(f"{'='*20} 开始测试 {'='*20}")
        test_start_time = time.time()
        trainer.test(model, datamodule=datamodule)
        test_end_time = time.time()
        test_duration = datetime.timedelta(seconds=int(test_end_time - test_start_time))
        print(f"{'='*20} 测试结束 {'='*20}")
        print(f"测试时间: {test_duration}")
    else:
        print(f"{'='*20} 仅测试模式 {'='*20}")
        test_start_time = time.time()
        trainer.test(model, datamodule=datamodule, ckpt_path=cfg.general.test_only)
        test_end_time = time.time()
        test_duration = datetime.timedelta(seconds=int(test_end_time - test_start_time))
        print(f"{'='*20} 测试结束 {'='*20}")
        print(f"测试时间: {test_duration}")

if __name__ == "__main__":
    main()
