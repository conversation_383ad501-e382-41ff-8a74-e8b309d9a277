import torch
import torch.nn as nn
import math

class TimestepEmbedder(nn.Module):
    """
    将标量时间步嵌入为向量表示。
    """
    def __init__(self, hidden_size, frequency_embedding_size=256):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(frequency_embedding_size, hidden_size, bias=True),
            nn.SiLU(),
            nn.Linear(hidden_size, hidden_size, bias=True),
        )
        self.frequency_embedding_size = frequency_embedding_size

    @staticmethod
    def timestep_embedding(t, dim, max_period=10000):
        """
        创建正弦时间步嵌入。
        :param t: 一个一维张量，长度为批量大小，每个元素为时间步索引，可以为小数。
        :param dim: 输出嵌入的维度。
        :param max_period: 控制嵌入的最小频率。
        :return: (N, D) 形状的时间步嵌入张量。
        """
        # https://github.com/openai/glide-text2im/blob/main/glide_text2im/nn.py
        half = dim // 2
        freqs = torch.exp(
            -math.log(max_period) * torch.arange(start=0, end=half, dtype=torch.float32) / half
        ).to(device=t.device)
        args = t[:, None].float() * freqs[None]
        embedding = torch.cat([torch.cos(args), torch.sin(args)], dim=-1)
        if dim % 2:
            embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)
        return embedding

    def forward(self, t):
        t = t.view(-1)
        t_freq = self.timestep_embedding(t, self.frequency_embedding_size)
        t_emb = self.mlp(t_freq)
        return t_emb

class CategoricalEmbedder(nn.Module):
    """
    将类别条件（如数据来源）嵌入为向量表示。
    同时支持标签dropout，用于无分类器引导。
    """
    def __init__(self, num_classes, hidden_size, dropout_prob):
        super().__init__()
        use_cfg_embedding = dropout_prob > 0
        self.embedding_table = nn.Embedding(num_classes + use_cfg_embedding, hidden_size)
        self.num_classes = num_classes
        self.dropout_prob = dropout_prob

    def token_drop(self, labels, force_drop_ids=None):
        """
        随机丢弃标签，实现无分类器引导。
        """
        if force_drop_ids is None:
            drop_ids = torch.rand(labels.shape[0], device=labels.device) < self.dropout_prob
        else:
            drop_ids = force_drop_ids == 1
        labels = torch.where(drop_ids, self.num_classes, labels)
        return labels

    def forward(self, labels, train, force_drop_ids=None, t=None):
        labels = labels.long().view(-1)
        use_dropout = self.dropout_prob > 0
        if (train and use_dropout) or (force_drop_ids is not None):
            labels = self.token_drop(labels, force_drop_ids)
        embeddings = self.embedding_table(labels)
        if True and train:
            noise = torch.randn_like(embeddings)
            embeddings = embeddings + noise
        return embeddings
    
class ClusterContinuousEmbedder(nn.Module):
    def __init__(self, input_size, hidden_size, dropout_prob):
        super().__init__()
        use_cfg_embedding = dropout_prob > 0

        if use_cfg_embedding:
            self.embedding_drop = nn.Embedding(1, hidden_size)

        # 门控机制：生成gamma和beta
        self.film_mlp = nn.Sequential(
            nn.Linear(input_size, hidden_size * 2, bias=True),
            nn.ReLU(),
            nn.Linear(hidden_size * 2, hidden_size * 2, bias=True)
        )
        self.hidden_size = hidden_size
        self.dropout_prob = dropout_prob

    def forward(self, labels, train, force_drop_ids=None, timestep=None, features=None):
        """
        labels: 连续条件输入，形状(batch, input_size)
        features: 主特征输入，形状(batch, hidden_size)
        返回门控后的特征，形状(batch, hidden_size)
        """
        use_dropout = self.dropout_prob > 0
        if force_drop_ids is not None:
            drop_ids = force_drop_ids == 1
        else:
            drop_ids = None

        if (train and use_dropout):
            drop_ids_rand = torch.rand(labels.shape[0], device=labels.device) < self.dropout_prob
            if force_drop_ids is not None:
                drop_ids = torch.logical_or(drop_ids, drop_ids_rand)
            else:
                drop_ids = drop_ids_rand

        # 生成gamma和beta
        film_out = self.film_mlp(labels)
        gamma, beta = film_out.chunk(2, dim=-1)  # (batch, hidden_size), (batch, hidden_size)

        if drop_ids is not None:
            # 对dropout样本，使用embedding_drop的权重作为无条件门控
            gamma_drop = self.embedding_drop.weight[0].expand(labels.shape[0], self.hidden_size)
            beta_drop = self.embedding_drop.weight[0].expand(labels.shape[0], self.hidden_size)
            gamma = torch.where(drop_ids.unsqueeze(-1), gamma_drop, gamma)
            beta = torch.where(drop_ids.unsqueeze(-1), beta_drop, beta)

        # features为主特征，默认为全1（即只用门控参数）
        if features is None:
            features = torch.ones(labels.shape[0], self.hidden_size, device=labels.device)

        # 门控融合：FiLM
        embeddings = gamma * features + beta

        if train:
            noise = torch.randn_like(embeddings)
            embeddings = embeddings + noise
        return embeddings
