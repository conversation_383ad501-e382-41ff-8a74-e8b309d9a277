import torch
import torch.nn as nn
import numpy as np
from rdkit import Chem
from rdkit.Chem import BRICS
from rdkit.Chem.Scaffolds import MurckoScaffold

class StructureMask:
    """
    用于识别和处理分子子结构的类，基于SME项目的方法
    """
    def __init__(self, max_n_nodes):
        self.max_n_nodes = max_n_nodes
    
    def get_brics_substructures(self, smiles_list):
        """
        获取分子的BRICS子结构
        
        Args:
            smiles_list: 分子SMILES字符串列表
            
        Returns:
            substructure_masks: 子结构掩码张量 (batch_size, max_n_nodes)
            substructure_indices: 每个分子的子结构索引列表
        """
        batch_size = len(smiles_list)
        substructure_masks = torch.zeros(batch_size, self.max_n_nodes)
        substructure_indices = []
        
        for i, smiles in enumerate(smiles_list):
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                substructure_indices.append([])
                continue
                
            # 获取BRICS断裂点
            res = list(BRICS.FindBRICSBonds(mol))
            all_brics_bond = [set(res[j][0]) for j in range(len(res))]
            
            # 获取所有BRICS原子
            all_brics_atom = []
            for brics_bond in all_brics_bond:
                all_brics_atom = list(set(all_brics_atom + list(brics_bond)))
            
            if len(all_brics_atom) > 0:
                # 获取断裂原子
                all_break_atom = {}
                for brics_atom in all_brics_atom:
                    brics_break_atom = []
                    for brics_bond in all_brics_bond:
                        if brics_atom in brics_bond:
                            brics_break_atom += list(set(brics_bond))
                    brics_break_atom = [x for x in brics_break_atom if x != brics_atom]
                    all_break_atom[brics_atom] = brics_break_atom
                
                # 获取子结构索引
                substrate_idx = {}
                used_atom = []
                for initial_atom_idx, break_atoms_idx in all_break_atom.items():
                    if initial_atom_idx not in used_atom:
                        neighbor_idx = [initial_atom_idx]
                        substrate_idx_i = neighbor_idx.copy()
                        begin_atom_idx_list = [initial_atom_idx]
                        while len(neighbor_idx) != 0:
                            for idx in begin_atom_idx_list:
                                initial_atom = mol.GetAtomWithIdx(idx)
                                neighbor_idx = neighbor_idx + [neighbor_atom.GetIdx() for neighbor_atom in
                                                            initial_atom.GetNeighbors()]
                                exlude_idx = all_break_atom[initial_atom_idx] + substrate_idx_i
                                if idx in all_break_atom.keys():
                                    exlude_idx = all_break_atom[initial_atom_idx] + substrate_idx_i + all_break_atom[idx]
                                neighbor_idx = [x for x in neighbor_idx if x not in exlude_idx]
                                substrate_idx_i += neighbor_idx
                                begin_atom_idx_list += neighbor_idx
                            begin_atom_idx_list = [x for x in begin_atom_idx_list if x not in substrate_idx_i]
                            neighbor_idx = [x for x in neighbor_idx if x not in substrate_idx_i]
                        substrate_idx[initial_atom_idx] = substrate_idx_i
                        used_atom += substrate_idx_i
            else:
                substrate_idx = {}
                substrate_idx[0] = [x for x in range(mol.GetNumAtoms())]
            
            # 创建子结构掩码
            mol_substruct_indices = []
            for key, indices in substrate_idx.items():
                mol_substruct_indices.append(indices)
                for idx in indices:
                    if idx < self.max_n_nodes:
                        substructure_masks[i, idx] = 1
            
            substructure_indices.append(mol_substruct_indices)
        
        return substructure_masks, substructure_indices
    
    def get_murcko_substructures(self, smiles_list):
        """
        获取分子的Murcko支架子结构
        
        Args:
            smiles_list: 分子SMILES字符串列表
            
        Returns:
            substructure_masks: 子结构掩码张量 (batch_size, max_n_nodes)
            scaffold_smiles: Murcko支架的SMILES字符串列表
        """
        batch_size = len(smiles_list)
        substructure_masks = torch.zeros(batch_size, self.max_n_nodes)
        scaffold_smiles = []
        
        for i, smiles in enumerate(smiles_list):
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                scaffold_smiles.append("")
                continue
            
            # 获取Murcko支架
            scaffold = MurckoScaffold.GetScaffoldForMol(mol)
            scaffold_smi = Chem.MolToSmiles(scaffold) if scaffold.GetNumAtoms() > 0 else ""
            scaffold_smiles.append(scaffold_smi)
            
            # 创建支架掩码
            if scaffold.GetNumAtoms() > 0:
                scaffold_match = mol.GetSubstructMatch(scaffold)
                for idx in scaffold_match:
                    if idx < self.max_n_nodes:
                        substructure_masks[i, idx] = 1
        
        return substructure_masks, scaffold_smiles
    
    def combine_substructures(self, smiles, substructure_indices, attribution_scores):
        """
        基于归因分数组合子结构
        
        Args:
            smiles: 分子SMILES字符串
            substructure_indices: 子结构索引列表
            attribution_scores: 原子级归因分数
            
        Returns:
            combined_indices: 组合后的子结构索引
            combined_scores: 组合后的子结构分数
        """
        mol = Chem.MolFromSmiles(smiles)
        if mol is None or not substructure_indices:
            return [], []
        
        combined_indices = []
        combined_scores = []
        
        for indices in substructure_indices:
            if not indices:
                continue
            
            # 计算子结构的平均归因分数
            substruct_score = np.mean([attribution_scores[idx] for idx in indices if idx < len(attribution_scores)])
            combined_indices.append(indices)
            combined_scores.append(substruct_score)
        
        # 按分数排序
        sorted_idx = np.argsort(combined_scores)[::-1]
        combined_indices = [combined_indices[idx] for idx in sorted_idx]
        combined_scores = [combined_scores[idx] for idx in sorted_idx]
        
        return combined_indices, combined_scores
    
    def get_substructure_features(self, mol, substructure_indices):
        """
        获取子结构特征
        
        Args:
            mol: RDKit分子对象
            substructure_indices: 子结构索引列表
            
        Returns:
            substructure_features: 子结构特征列表
        """
        if mol is None or not substructure_indices:
            return []
        
        substructure_features = []
        
        for indices in substructure_indices:
            if not indices:
                continue
            
            # 计算子结构特征
            num_atoms = len(indices)
            num_bonds = 0
            num_rings = 0
            atom_types = set()
            
            for idx in indices:
                atom = mol.GetAtomWithIdx(idx)
                atom_types.add(atom.GetSymbol())
                
                for bond in atom.GetBonds():
                    begin_atom = bond.GetBeginAtomIdx()
                    end_atom = bond.GetEndAtomIdx()
                    if begin_atom in indices and end_atom in indices:
                        num_bonds += 1
            
            # 避免重复计算
            num_bonds = num_bonds // 2
            
            # 计算环的数量
            if num_atoms > 0:
                submol = Chem.PathToSubmol(mol, indices)
                num_rings = submol.GetRingInfo().NumRings() if submol is not None else 0
            
            features = {
                'num_atoms': num_atoms,
                'num_bonds': num_bonds,
                'num_rings': num_rings,
                'atom_types': list(atom_types),
                'diversity': len(atom_types) / max(1, num_atoms)
            }
            
            substructure_features.append(features)
        
        return substructure_features


class SubstructureAttention(nn.Module):
    """
    子结构注意力模块，用于在扩散过程中关注重要子结构
    """
    def __init__(self, hidden_size):
        super(SubstructureAttention, self).__init__()
        self.attention = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x, substructure_mask):
        """
        Args:
            x: 节点特征 (batch_size, num_nodes, hidden_size)
            substructure_mask: 子结构掩码 (batch_size, num_nodes)
            
        Returns:
            weighted_x: 加权后的节点特征
            attention_weights: 注意力权重
        """
        # 计算注意力权重
        attention_weights = self.attention(x).squeeze(-1)  # (batch_size, num_nodes)
        
        # 应用子结构掩码
        masked_attention = attention_weights * substructure_mask
        
        # 归一化注意力权重
        sum_attention = torch.sum(masked_attention, dim=1, keepdim=True) + 1e-10
        normalized_attention = masked_attention / sum_attention
        
        # 应用注意力权重
        weighted_x = x * normalized_attention.unsqueeze(-1)
        
        return weighted_x, normalized_attention


class SubstructureGuidance(nn.Module):
    """
    子结构引导模块，用于扩散模型中的条件生成
    """
    def __init__(self, hidden_size, guide_scale=2.0):
        super(SubstructureGuidance, self).__init__()
        self.guide_scale = guide_scale
        self.substructure_encoder = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size)
        )
    
    def forward(self, x, substructure_mask, node_mask):
        """
        Args:
            x: 节点特征 (batch_size, num_nodes, hidden_size)
            substructure_mask: 子结构掩码 (batch_size, num_nodes)
            node_mask: 节点掩码 (batch_size, num_nodes)
            
        Returns:
            guided_x: 引导后的节点特征
        """
        # 应用节点掩码
        valid_mask = node_mask * substructure_mask
        
        # 计算子结构特征
        substructure_features = torch.sum(x * valid_mask.unsqueeze(-1), dim=1) / (torch.sum(valid_mask, dim=1, keepdim=True) + 1e-10)
        
        # 编码子结构特征
        encoded_features = self.substructure_encoder(substructure_features)
        
        # 应用引导
        guidance = encoded_features.unsqueeze(1) * valid_mask.unsqueeze(-1) * self.guide_scale
        guided_x = x + guidance
        
        return guided_x 