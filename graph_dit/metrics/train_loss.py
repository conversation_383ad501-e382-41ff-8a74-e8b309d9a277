import time
import torch
import torch.nn as nn
import torch.nn.functional as F
from metrics.abstract_metrics import CrossEntropyMetric
from torchmetrics import Metric, MeanSquaredError

# from 2:He to 119:*
valencies_check = [0, 1, 2, 3, 4, 3, 2, 1, 0, 1, 2, 6, 6, 7, 6, 1, 0, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 7, 6, 1, 0, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 7, 6, 5, 6, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 7, 6, 5, 0, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
valencies_check = torch.tensor(valencies_check)

weight_check = [4.003, 6.941, 9.012, 10.812, 12.011, 14.007, 15.999, 18.998, 20.18, 22.99, 24.305, 26.982, 28.086, 30.974, 32.067, 35.453, 39.948, 39.098, 40.078, 44.956, 47.867, 50.942, 51.996, 54.938, 55.845, 58.933, 58.693, 63.546, 65.39, 69.723, 72.61, 74.922, 78.96, 79.904, 83.8, 85.468, 87.62, 88.906, 91.224, 92.906, 95.94, 98.0, 101.07, 102.906, 106.42, 107.868, 112.412, 114.818, 118.711, 121.76, 127.6, 126.904, 131.29, 132.905, 137.328, 138.906, 140.116, 140.908, 144.24, 145.0, 150.36, 151.964, 157.25, 158.925, 162.5, 164.93, 167.26, 168.934, 173.04, 174.967, 178.49, 180.948, 183.84, 186.207, 190.23, 192.217, 195.078, 196.967, 200.59, 204.383, 207.2, 208.98, 209.0, 210.0, 222.0, 223.0, 226.0, 227.0, 232.038, 231.036, 238.029, 237.0, 244.0, 243.0, 247.0, 247.0, 251.0, 252.0, 257.0, 258.0, 259.0, 262.0, 267.0, 268.0, 269.0, 270.0, 269.0, 278.0, 281.0, 281.0, 285.0, 284.0, 289.0, 288.0, 293.0, 292.0, 294.0, 294.0]
weight_check = torch.tensor(weight_check)

# 扩展的原子价态字典 (原子序数: 允许的价态列表)
EXTENDED_ATOM_VALENCY = {
    1: [1],                    # H
    6: [2, 3, 4],             # C (考虑不饱和度)
    7: [1, 2, 3, 4, 5],       # N (考虑不同氧化态)
    8: [1, 2],                # O (考虑自由基)
    9: [1],                   # F
    15: [3, 5],               # P
    16: [2, 4, 6],            # S
    17: [1, 3, 5, 7],         # Cl (考虑高氧化态)
    35: [1, 3, 5],            # Br
    53: [1, 3, 5, 7]          # I
}

# 原子间允许的键类型矩阵 (原子序数对: 允许的键类型)
ALLOWED_BOND_TYPES = {
    # (atom1, atom2): [allowed_bond_types]
    (1, 1): [1],              # H-H: 只能单键
    (1, 6): [1],              # H-C: 只能单键
    (1, 7): [1],              # H-N: 只能单键
    (1, 8): [1],              # H-O: 只能单键
    (6, 6): [1, 2, 3, 4],     # C-C: 所有键类型
    (6, 7): [1, 2, 3, 4],     # C-N: 所有键类型
    (6, 8): [1, 2],           # C-O: 单键和双键
    (7, 7): [1, 2, 3],        # N-N: 单键、双键、三键
    (7, 8): [1, 2],           # N-O: 单键和双键
    (8, 8): [1, 2],           # O-O: 单键和双键
    # 卤素原子通常只形成单键
    (9, 6): [1], (9, 7): [1], (9, 8): [1],   # F
    (17, 6): [1], (17, 7): [1], (17, 8): [1], # Cl
    (35, 6): [1], (35, 7): [1], (35, 8): [1], # Br
    (53, 6): [1], (53, 7): [1], (53, 8): [1], # I
}

# 键类型对应的键级
BOND_ORDER = {
    0: 0,  # NO_BOND
    1: 1,  # SINGLE
    2: 2,  # DOUBLE
    3: 3,  # TRIPLE
    4: 1.5 # AROMATIC (近似为1.5)
}

class AtomWeightMetric(Metric):
    def __init__(self):
        super().__init__()
        self.add_state('total_loss', default=torch.tensor(0.), dist_reduce_fx="sum")
        self.add_state('total_samples', default=torch.tensor(0.), dist_reduce_fx="sum")
        global weight_check
        self.weight_check = weight_check

    def update(self, X, Y):
        atom_pred_num = X.argmax(dim=-1)
        atom_real_num = Y.argmax(dim=-1)
        self.weight_check = self.weight_check.type_as(X)

        pred_weight = self.weight_check[atom_pred_num]
        real_weight = self.weight_check[atom_real_num]

        lss = 0
        lss += torch.abs(pred_weight.sum(dim=-1) - real_weight.sum(dim=-1)).sum()
        self.total_loss += lss
        self.total_samples += X.size(0)

    def compute(self):
        return self.total_loss / self.total_samples


class ChemicalConstraintLoss(nn.Module):
    """化学约束损失函数"""

    def __init__(self, valency_weight=1.0, bond_type_weight=1.0, connectivity_weight=0.5):
        super().__init__()
        self.valency_weight = valency_weight
        self.bond_type_weight = bond_type_weight
        self.connectivity_weight = connectivity_weight

    def forward(self, pred_X, pred_E, node_mask, active_index):
        """
        计算化学约束损失

        Args:
            pred_X: 预测的原子类型概率 (bs, n, num_atom_types)
            pred_E: 预测的边类型概率 (bs, n, n, num_edge_types)
            node_mask: 节点掩码 (bs, n)
            active_index: 活跃原子索引映射
        """
        total_loss = 0.0

        # 1. 原子价态约束损失
        valency_loss = self._compute_valency_constraint_loss(pred_X, pred_E, node_mask, active_index)
        total_loss += self.valency_weight * valency_loss

        # 2. 键类型合理性约束损失
        bond_type_loss = self._compute_bond_type_constraint_loss(pred_X, pred_E, node_mask, active_index)
        total_loss += self.bond_type_weight * bond_type_loss

        # 3. 连通性约束损失
        connectivity_loss = self._compute_connectivity_constraint_loss(pred_E, node_mask)
        total_loss += self.connectivity_weight * connectivity_loss

        return total_loss

    def _compute_valency_constraint_loss(self, pred_X, pred_E, node_mask, active_index):
        """计算原子价态约束损失"""
        bs, n, _ = pred_X.shape
        device = pred_X.device

        # 获取最可能的原子类型
        atom_types = torch.argmax(pred_X, dim=-1)  # (bs, n)

        valency_loss = 0.0
        valid_samples = 0

        for b in range(bs):
            for i in range(n):
                if not node_mask[b, i]:
                    continue

                atom_type_idx = atom_types[b, i].item()
                # 转换为实际原子序数
                atom_num = active_index[atom_type_idx] + 1 if atom_type_idx < len(active_index) else atom_type_idx + 1

                # 计算该原子的总价态（基于边概率）
                total_valency = 0.0
                for j in range(n):
                    if i != j and node_mask[b, j]:
                        edge_probs = F.softmax(pred_E[b, i, j], dim=-1)
                        # 计算期望价态
                        for bond_type in range(1, len(edge_probs)):  # 跳过NO_BOND
                            bond_order = BOND_ORDER.get(bond_type, 1)
                            total_valency += edge_probs[bond_type] * bond_order

                # 检查是否违反价态约束
                allowed_valencies = EXTENDED_ATOM_VALENCY.get(atom_num, [4])
                min_allowed = min(allowed_valencies)
                max_allowed = max(allowed_valencies)

                # 计算价态违规损失
                if total_valency < min_allowed:
                    valency_loss += (min_allowed - total_valency) ** 2
                elif total_valency > max_allowed:
                    valency_loss += (total_valency - max_allowed) ** 2

                valid_samples += 1

        return valency_loss / max(valid_samples, 1)

    def _compute_bond_type_constraint_loss(self, pred_X, pred_E, node_mask, active_index):
        """计算键类型合理性约束损失"""
        bs, n, _ = pred_X.shape

        # 获取最可能的原子类型
        atom_types = torch.argmax(pred_X, dim=-1)  # (bs, n)

        bond_type_loss = 0.0
        valid_pairs = 0

        for b in range(bs):
            for i in range(n):
                for j in range(i + 1, n):
                    if not (node_mask[b, i] and node_mask[b, j]):
                        continue

                    atom1_type_idx = atom_types[b, i].item()
                    atom2_type_idx = atom_types[b, j].item()

                    # 转换为实际原子序数
                    atom1_num = active_index[atom1_type_idx] + 1 if atom1_type_idx < len(active_index) else atom1_type_idx + 1
                    atom2_num = active_index[atom2_type_idx] + 1 if atom2_type_idx < len(active_index) else atom2_type_idx + 1

                    # 获取允许的键类型
                    key = tuple(sorted([atom1_num, atom2_num]))
                    allowed_bonds = ALLOWED_BOND_TYPES.get(key, [1, 2, 3, 4])

                    # 计算不允许键类型的概率
                    edge_probs = F.softmax(pred_E[b, i, j], dim=-1)

                    for bond_type in range(1, len(edge_probs)):  # 跳过NO_BOND
                        if bond_type not in allowed_bonds:
                            bond_type_loss += edge_probs[bond_type]

                    valid_pairs += 1

        return bond_type_loss / max(valid_pairs, 1)

    def _compute_connectivity_constraint_loss(self, pred_E, node_mask):
        """计算连通性约束损失（鼓励生成连通的分子）"""
        bs, n, _, _ = pred_E.shape

        connectivity_loss = 0.0

        for b in range(bs):
            # 计算边的存在概率
            edge_exist_probs = 1 - F.softmax(pred_E[b], dim=-1)[:, :, 0]  # 1 - P(NO_BOND)

            # 构建邻接矩阵
            adj_matrix = edge_exist_probs * node_mask[b].unsqueeze(0) * node_mask[b].unsqueeze(1)

            # 简单的连通性检查：确保每个节点至少有一条边
            node_degrees = torch.sum(adj_matrix, dim=-1)
            isolated_penalty = torch.sum(torch.relu(1.0 - node_degrees) * node_mask[b])

            connectivity_loss += isolated_penalty

        return connectivity_loss / bs


class TrainLossDiscrete(nn.Module):
    """ Train with Cross entropy and chemical constraints"""
    def __init__(self, lambda_train, weight_node=None, weight_edge=None,
                 use_chemical_constraints=True, chemical_constraint_weights=None):
        super().__init__()
        self.node_loss = CrossEntropyMetric()
        self.edge_loss = CrossEntropyMetric()
        self.weight_loss = AtomWeightMetric()

        self.y_loss = MeanSquaredError()
        self.lambda_train = lambda_train

        # 化学约束相关
        self.use_chemical_constraints = use_chemical_constraints
        if self.use_chemical_constraints:
            if chemical_constraint_weights is None:
                chemical_constraint_weights = {'valency': 0.1, 'bond_type': 0.1, 'connectivity': 0.05}
            self.chemical_constraint_loss = ChemicalConstraintLoss(
                valency_weight=chemical_constraint_weights.get('valency', 0.1),
                bond_type_weight=chemical_constraint_weights.get('bond_type', 0.1),
                connectivity_weight=chemical_constraint_weights.get('connectivity', 0.05)
            )

    def forward(self, masked_pred_X, masked_pred_E, pred_y, true_X, true_E, true_y, node_mask, log: bool, active_index=None):
        """ Compute train metrics with chemical constraints
        masked_pred_X : tensor -- (bs, n, dx)
        masked_pred_E : tensor -- (bs, n, n, de)
        pred_y : tensor -- (bs, )
        true_X : tensor -- (bs, n, dx)
        true_E : tensor -- (bs, n, n, de)
        true_y : tensor -- (bs, )
        node_mask : tensor -- (bs, n)
        log : boolean
        active_index : list -- mapping from model indices to actual atomic numbers
        """

        loss_weight = self.weight_loss(masked_pred_X, true_X)

        # 保存原始形状用于化学约束计算
        orig_pred_X = masked_pred_X.clone()
        orig_pred_E = masked_pred_E.clone()

        true_X = torch.reshape(true_X, (-1, true_X.size(-1)))  # (bs * n, dx)
        true_E = torch.reshape(true_E, (-1, true_E.size(-1)))  # (bs * n * n, de)
        masked_pred_X = torch.reshape(masked_pred_X, (-1, masked_pred_X.size(-1)))  # (bs * n, dx)
        masked_pred_E = torch.reshape(masked_pred_E, (-1, masked_pred_E.size(-1)))   # (bs * n * n, de)

        # Remove masked rows
        mask_X = (true_X != 0.).any(dim=-1)
        mask_E = (true_E != 0.).any(dim=-1)

        flat_true_X = true_X[mask_X, :]
        flat_pred_X = masked_pred_X[mask_X, :]

        flat_true_E = true_E[mask_E, :]
        flat_pred_E = masked_pred_E[mask_E, :]

        loss_X = self.node_loss(flat_pred_X, flat_true_X) if true_X.numel() > 0 else 0.0
        loss_E = self.edge_loss(flat_pred_E, flat_true_E) if true_E.numel() > 0 else 0.0

        # 基础损失
        base_loss = self.lambda_train[0] * loss_X + self.lambda_train[1] * loss_E + loss_weight

        # 添加化学约束损失（降低计算频率以提升性能）
        if self.use_chemical_constraints and active_index is not None:
            try:
                # 只在每10个batch计算一次化学约束损失以提升性能
                import random
                if random.random() < 0.1:  # 10%的概率计算化学约束
                    chemical_loss = self.chemical_constraint_loss(orig_pred_X, orig_pred_E, node_mask, active_index)
                    total_loss = base_loss + chemical_loss

                    if log:
                        print(f"  Chemical constraint loss: {chemical_loss:.6f}")
                else:
                    total_loss = base_loss

            except Exception as e:
                if log:
                    print(f"  Warning: Chemical constraint loss computation failed: {e}")
                total_loss = base_loss
        else:
            total_loss = base_loss

        return total_loss

    def reset(self):
        for metric in [self.node_loss, self.edge_loss, self.y_loss, self.weight_loss]:
            metric.reset()

    def log_epoch_metrics(self, current_epoch, start_epoch_time, log=True):
        epoch_node_loss = self.node_loss.compute() if self.node_loss.total_samples > 0 else -1
        epoch_edge_loss = self.edge_loss.compute() if self.edge_loss.total_samples > 0 else -1
        epoch_weight_loss = self.weight_loss.compute() if self.weight_loss.total_samples > 0 else -1

        if log:
            print(f"Epoch {current_epoch} finished: X_CE: {epoch_node_loss :.4f} -- E_CE: {epoch_edge_loss :.4f} "
                f"Weight: {epoch_weight_loss :.4f} "
                f"-- Time taken {time.time() - start_epoch_time:.1f}s ")